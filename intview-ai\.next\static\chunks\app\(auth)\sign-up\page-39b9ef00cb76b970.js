(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[220],{2257:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>i});var t=a(5155),s=a(3936),n=a(3582);a(2115);let i=()=>(0,t.jsx)(s.A,{formType:"SIGN_UP",schema:n.m,defaultValues:{email:"",name:"",password:"",confirmPassword:""},onSubmit:e=>Promise.resolve({success:!0,data:e})})},3582:(e,r,a)=>{"use strict";a.d(r,{I:()=>s,m:()=>n});var t=a(8309);let s=t.Ik({email:t.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:t.Yj().min(6,{error:"Password must be atleast 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"})}),n=t.Ik({confirmPassword:t.Yj().min(6,{error:"Password must be atleast 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain atleast one uppercase character").regex(/[a-z]/,"Password must contain atleast one lowercase character").regex(/[0-9]/,"Password must contain atleast one number").regex(/[^a-zA-Z0-9]/,"Password must contain atleast one special character"),name:t.Yj().min(1,{message:"Name is required."}).max(50,{message:"Name cannot exceed 50 characters."}).regex(/^[a-zA-Z\s]+$/,{message:"Name can only contain letters and spaces."}),email:t.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:t.Yj().min(6,{error:"Password must be atleast 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain atleast one uppercase character").regex(/[a-z]/,"Password must contain atleast one lowercase character").regex(/[0-9]/,"Password must contain atleast one number").regex(/[^a-zA-Z0-9]/,"Password must contain atleast one special character")})},3936:(e,r,a)=>{"use strict";a.d(r,{A:()=>k});var t=a(5155),s=a(221),n=a(2177),i=a(7168),o=a(2115),d=a(4624),l=a(3999),c=a(7073);function u(e){let{className:r,...a}=e;return(0,t.jsx)(c.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...a})}let m=n.Op,g=o.createContext({}),p=e=>{let{...r}=e;return(0,t.jsx)(g.Provider,{value:{name:r.name},children:(0,t.jsx)(n.xI,{...r})})},x=()=>{let e=o.useContext(g),r=o.useContext(h),{getFieldState:a}=(0,n.xW)(),t=(0,n.lN)({name:e.name}),s=a(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=r;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...s}},h=o.createContext({});function f(e){let{className:r,...a}=e,s=o.useId();return(0,t.jsx)(h.Provider,{value:{id:s},children:(0,t.jsx)("div",{"data-slot":"form-item",className:(0,l.cn)("grid gap-2",r),...a})})}function v(e){let{className:r,...a}=e,{error:s,formItemId:n}=x();return(0,t.jsx)(u,{"data-slot":"form-label","data-error":!!s,className:(0,l.cn)("data-[error=true]:text-destructive",r),htmlFor:n,...a})}function b(e){let{...r}=e,{error:a,formItemId:s,formDescriptionId:n,formMessageId:i}=x();return(0,t.jsx)(d.DX,{"data-slot":"form-control",id:s,"aria-describedby":a?"".concat(n," ").concat(i):"".concat(n),"aria-invalid":!!a,...r})}function w(e){var r;let{className:a,...s}=e,{error:n,formMessageId:i}=x(),o=n?String(null!=(r=null==n?void 0:n.message)?r:""):s.children;return o?(0,t.jsx)("p",{"data-slot":"form-message",id:i,className:(0,l.cn)("text-destructive text-sm",a),...s,children:o}):null}function j(e){let{className:r,type:a,...s}=e;return(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,l.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...s})}var N=a(6874),y=a.n(N);let P={SIGN_IN:"/sign-in",SIGN_UP:"/sign-up"},k=e=>{let{schema:r,defaultValues:a,formType:o}=e,d=(0,n.mN)({resolver:(0,s.u)(r),defaultValues:a}),l=async()=>{},c="SIGN_IN"===o?"Sign In":"Sign Up";return(0,t.jsx)(m,{...d,children:(0,t.jsxs)("form",{onSubmit:d.handleSubmit(l),className:"mt-10 space-y-6",children:[Object.keys(a).map(e=>(0,t.jsx)(p,{control:d.control,name:e,render:e=>{let{field:r}=e;return(0,t.jsxs)(f,{className:"flex w-full flex-col gap-2.5 ",children:[(0,t.jsx)(v,{className:"paragraph-medium text-dark400_light700",children:"email"===r.name?"Email Address":r.name.charAt(0).toUpperCase()+r.name.slice(1)}),(0,t.jsx)(b,{children:(0,t.jsx)(j,{type:"password"===r.name?"password":"text",...r,className:"paragraph-regular background-light900_dark300   light-border-2 text-dark300_light700 no-focus min-h-12   rounded-1.5 border"})}),(0,t.jsx)(w,{})]})}},e)),(0,t.jsx)(i.$,{className:"primary-button paragraph-medium w-full   min-h-12 rounded-4xl px-4 py-3 font-inter !text-light-900   ",disabled:d.formState.isSubmitting,children:d.formState.isSubmitting?"Sign In"===c?"Signin In...":"Signin Up...":c}),"SIGN_IN"===o?(0,t.jsxs)("p",{children:["Don't have an account? ",(0,t.jsx)(y(),{href:P.SIGN_UP,className:"paragraph-semibold primary-text",children:"Sign Up"})]}):(0,t.jsxs)("p",{children:["Already have an account?"," ",(0,t.jsx)(y(),{href:P.SIGN_IN,className:"paragraph-semibold primary-text",children:"Sign In"})]})]})})}},3999:(e,r,a)=>{"use strict";a.d(r,{cn:()=>n});var t=a(2596),s=a(9688);function n(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return(0,s.QP)((0,t.$)(r))}},5217:(e,r,a)=>{Promise.resolve().then(a.bind(a,2257))},7168:(e,r,a)=>{"use strict";a.d(r,{$:()=>d});var t=a(5155);a(2115);var s=a(4624),n=a(2085),i=a(3999);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:r,variant:a,size:n,asChild:d=!1,...l}=e,c=d?s.DX:"button";return(0,t.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:a,size:n,className:r})),...l})}}},e=>{var r=r=>e(e.s=r);e.O(0,[13,874,185,441,684,358],()=>r(5217)),_N_E=e.O()}]);