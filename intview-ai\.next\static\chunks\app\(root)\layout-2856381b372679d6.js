(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[575],{546:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var r=s(5155),a=s(2115),l=s(9946);let n=(0,l.A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),i=(0,l.A)("bell-dot",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M13.916 2.314A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.74 7.327A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673 9 9 0 0 1-.585-.665",key:"1tip0g"}],["circle",{cx:"18",cy:"8",r:"3",key:"1g0gzu"}]]),c=(0,l.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),d=(0,l.A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),o=(0,l.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),x=(0,l.A)("circle-user",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}],["path",{d:"M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662",key:"154egf"}]]),m=e=>{let{onToggleSidebar:t}=e,[s,l]=(0,a.useState)(!1);return(0,r.jsxs)("header",{className:"border-b bg-white px-4 sm:px-6 py-4 sm:py-5 shrink-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("button",{onClick:t,className:"lg:hidden p-2 hover:bg-gray-100 rounded-lg transition-colors","aria-label":"Toggle sidebar",children:(0,r.jsx)(n,{className:"h-5 w-5 text-gray-600"})}),(0,r.jsx)("div",{className:"text-lg sm:text-xl font-semibold text-gray-900",children:"AI Interview"})]}),(0,r.jsxs)("div",{className:"hidden lg:flex items-center gap-4 xl:gap-6",children:[(0,r.jsx)(i,{className:"h-6 w-6 sm:h-8 sm:w-7 text-gray-700 cursor-pointer hover:text-gray-800 transition-colors"}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-700 cursor-pointer bg-gray-50 py-2 sm:py-4 px-4 sm:px-6 rounded-full transition-colors",children:[(0,r.jsx)(c,{className:"h-5 w-5 sm:h-6 sm:w-6"}),(0,r.jsx)("span",{className:"font-bold hidden sm:inline",children:"English"}),(0,r.jsx)(d,{className:"h-4 w-4 sm:h-5 sm:w-5 rounded-full bg-[#dddae5] border border-[#aba6bb] text-[#aba6bb]"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 cursor-pointer bg-gray-50 rounded-full px-4 sm:px-8 py-2 sm:py-3 transition-colors",children:[(0,r.jsx)("div",{className:"h-6 w-6 sm:h-8 sm:w-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-xs sm:text-sm font-medium text-gray-500 p-1"})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"text-xs sm:text-sm font-bold text-gray-900",children:"Hammad M"}),(0,r.jsx)("span",{className:"text-xs text-purple-600",children:"Free"})]}),(0,r.jsx)(d,{className:"h-4 w-4 sm:h-5 sm:w-5 rounded-full bg-[#dfdbe9] border border-[#aba6bb] text-[#aba6bb]"})]})]}),(0,r.jsxs)("div",{className:"hidden md:flex lg:hidden items-center gap-3",children:[(0,r.jsx)(i,{className:"h-6 w-6 text-gray-700 cursor-pointer hover:text-gray-800 transition-colors"}),(0,r.jsxs)("div",{className:"flex items-center gap-2 cursor-pointer bg-gray-50 rounded-full px-3 py-2 transition-colors",children:[(0,r.jsx)("div",{className:"h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-xs font-medium text-gray-500",children:"H"})}),(0,r.jsx)("span",{className:"text-sm font-bold text-gray-900",children:"Hammad"}),(0,r.jsx)(d,{className:"h-4 w-4 rounded-full bg-[#dfdbe9] border border-[#aba6bb] text-[#aba6bb]"})]})]}),(0,r.jsx)("div",{className:"sm:hidden",children:(0,r.jsx)("button",{onClick:()=>{l(!s)},className:"p-2",children:s?(0,r.jsx)(o,{className:"h-6 w-6"}):(0,r.jsx)(x,{className:"h-6 w-6 text-gray-700"})})})]}),s&&(0,r.jsxs)("div",{className:"mt-4 pb-4 flex flex-col gap-4 sm:hidden border-t pt-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg bg-gray-50",children:[(0,r.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"H"})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"text-sm font-bold text-gray-900",children:"Hammad M"}),(0,r.jsx)("span",{className:"text-xs text-purple-600",children:"Free"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(i,{className:"h-5 w-5 text-gray-700"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Notifications"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(c,{className:"h-5 w-5 text-gray-700"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"English"})]})]})]})};var h=s(6766),u=s(6874),g=s.n(u),f=s(8999);let p={src:"/_next/static/media/logo-light.a0bdc026.svg",height:62,width:177,blurWidth:0,blurHeight:0},y=[{label:"Dashboard",href:"/",icon:(0,l.A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},{label:"Job Posts",href:"/interview",icon:s(6325).A}],b=e=>{let{isOpen:t,onClose:s}=e,l=(0,f.usePathname)(),n=(0,a.useRef)(l);return(0,a.useEffect)(()=>{n.current!==l&&t&&s&&s(),n.current=l},[l,t,s]),(0,a.useEffect)(()=>{let e=e=>{let t=document.getElementById("mobile-sidebar"),r=document.getElementById("sidebar-overlay");t&&!t.contains(e.target)&&(null==r?void 0:r.contains(e.target))&&s&&s()};return t?(document.addEventListener("mousedown",e),document.body.style.overflow="hidden"):document.body.style.overflow="unset",()=>{document.removeEventListener("mousedown",e),document.body.style.overflow="unset"}},[t,s]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("aside",{className:"hidden lg:flex w-54 h-full bg-white border-r p-6 flex-col shrink-0",children:[(0,r.jsx)("div",{className:"flex items-center gap-2 mb-10",children:(0,r.jsx)(h.default,{src:p,alt:"Logo"})}),(0,r.jsx)("nav",{className:"flex flex-col gap-4",children:y.map(e=>{let t=l===e.href,s=e.icon;return(0,r.jsxs)(g(),{href:e.href,className:"flex items-center gap-3 px-4 py-3 rounded-lg transition-all group\n                  ".concat(t?"bg-purple-100 text-purple-700 font-extrabold":"text-gray-400 hover:bg-gray-50 hover:text-gray-600"),children:[(0,r.jsx)(s,{className:"w-5 h-5 ".concat(t?"text-purple-700":"text-gray-400")}),(0,r.jsx)("span",{className:"text-sm font-medium",children:e.label})]},e.label)})})]}),(0,r.jsx)("div",{id:"sidebar-overlay",className:"fixed inset-0 bg-gray-300/50 bg-opacity-50 z-40 lg:hidden transition-opacity duration-300 ".concat(t?"opacity-100":"opacity-0 pointer-events-none"),children:(0,r.jsxs)("aside",{id:"mobile-sidebar",className:"fixed left-0 top-0 h-full w-64 bg-white border-r p-6 flex flex-col z-50 transform transition-transform duration-300 ease-in-out ".concat(t?"translate-x-0":"-translate-x-full"),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-10",children:[(0,r.jsx)(h.default,{src:p,alt:"Logo"}),(0,r.jsx)("button",{onClick:s,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(o,{className:"h-5 w-5 text-gray-500"})})]}),(0,r.jsx)("nav",{className:"flex flex-col gap-4",children:y.map(e=>{let t=l===e.href,a=e.icon;return(0,r.jsxs)(g(),{href:e.href,className:"flex items-center gap-3 px-4 py-3 rounded-lg transition-all group\n                      ".concat(t?"bg-purple-100 text-purple-700 font-extrabold":"text-gray-400 hover:bg-gray-50 hover:text-gray-600"),onClick:s,children:[(0,r.jsx)(a,{className:"w-5 h-5 ".concat(t?"text-purple-700":"text-gray-400")}),(0,r.jsx)("span",{className:"text-sm font-medium",children:e.label})]},e.label)})})]})})]})},j=e=>{let{children:t}=e,[s,l]=(0,a.useState)(!1),n=(0,a.useCallback)(()=>{l(e=>!e)},[]),i=(0,a.useCallback)(()=>{l(!1)},[]);return(0,r.jsxs)("div",{className:"flex h-screen bg-gray-50",children:[(0,r.jsx)(b,{isOpen:s,onClose:i}),(0,r.jsxs)("div",{className:"flex flex-col flex-1 overflow-hidden min-w-0",children:[(0,r.jsx)(m,{onToggleSidebar:n}),(0,r.jsx)("main",{className:"flex-1 overflow-auto p-4 sm:p-6",children:t})]})]})}},2807:(e,t,s)=>{Promise.resolve().then(s.bind(s,546))},6325:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("briefcase-business",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},6654:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return a}});let r=s(2115);function a(e,t){let s=(0,r.useRef)(null),a=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=s.current;e&&(s.current=null,e());let t=a.current;t&&(a.current=null,t())}else e&&(s.current=l(e,r)),t&&(a.current=l(t,r))},[e,t])}function l(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let s=e(t);return"function"==typeof s?s:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9946:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var r=s(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),n=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)((e,t)=>{let{color:s="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:o="",children:x,iconNode:m,...h}=e;return(0,r.createElement)("svg",{ref:t,...d,width:a,height:a,stroke:s,strokeWidth:n?24*Number(l)/Number(a):l,className:i("lucide",o),...!x&&!c(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(x)?x:[x]])}),x=(e,t)=>{let s=(0,r.forwardRef)((s,l)=>{let{className:c,...d}=s;return(0,r.createElement)(o,{ref:l,iconNode:t,className:i("lucide-".concat(a(n(e))),"lucide-".concat(e),c),...d})});return s.displayName=n(e),s}}},e=>{var t=t=>e(e.s=t);e.O(0,[766,874,441,684,358],()=>t(2807)),_N_E=e.O()}]);