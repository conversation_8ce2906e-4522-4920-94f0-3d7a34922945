(()=>{var e={};e.id=522,e.ids=[522],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2193:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var o=t(7413);let s=()=>(0,o.jsx)("div",{className:"flex-1 border border-gray-400 rounded-md h-screen",children:(0,o.jsx)("div",{className:"py-2 px-4",children:(0,o.jsx)("p",{className:"",children:"Instructions for Interview!"})})})},2481:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>c,tree:()=>p});var o=t(5239),s=t(8088),n=t(8170),i=t.n(n),a=t(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let p={children:["",{children:["(root)",{children:["job-posts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2193)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\job-posts\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,2528)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,8014)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\job-posts\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},c=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(root)/job-posts/page",pathname:"/job-posts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[97,423,715,762],()=>t(2481));module.exports=o})();