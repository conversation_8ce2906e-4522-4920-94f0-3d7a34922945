(()=>{var e={};e.id=492,e.ids=[492],e.modules={25:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});let o=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\context\\\\Theme.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\context\\Theme.tsx","default")},363:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>o});let o=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\sonner.tsx","Toaster")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1279:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var o=r(687);r(3210);var s=r(218);let n=({children:e,...t})=>(0,o.jsx)(s.N,{...t,children:e})},2704:()=>{},2910:(e,t,r)=>{Promise.resolve().then(r.bind(r,363)),Promise.resolve().then(r.bind(r,25)),Promise.resolve().then(r.bind(r,2175))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4013:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},4593:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>i});var o=r(687),s=r(218),n=r(2581);let i=({...e})=>{let{theme:t="system"}=(0,s.D)();return(0,o.jsx)(n.l$,{theme:t,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},5511:e=>{"use strict";e.exports=require("crypto")},5958:(e,t,r)=>{Promise.resolve().then(r.bind(r,4593)),Promise.resolve().then(r.bind(r,1279)),Promise.resolve().then(r.bind(r,9208))},6749:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},6814:(e,t,r)=>{"use strict";r.d(t,{Y9:()=>i,j2:()=>d});var o=r(9859),s=r(3560),n=r(6056);let{handlers:i,signIn:a,signOut:l,auth:d}=(0,o.Ay)({providers:[s.A,n.A],secret:process.env.AUTH_SECRET})},8014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>m});var o=r(7413),s=r(363),n=r(6649),i=r.n(n),a=r(5843),l=r.n(a);r(2704);var d=r(25),p=r(2175),u=r(6814);let m={title:"Interview AI",description:`A community driven platform for asking and answering programming questions. Get help, share knowledge 
  and collaborate with developers from arount the world. Explore topics in web development, mobile app development, 
  data structures, and more.`,icons:{icon:"/images/site-logo.svg"}},c=async({children:e})=>{let t=await (0,u.j2)();return(0,o.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,o.jsx)(p.SessionProvider,{session:t,children:(0,o.jsxs)("body",{className:`${i().className} ${l().variable} antialiased`,children:[(0,o.jsx)(d.default,{attribute:"class",defaultTheme:"light",children:e}),(0,o.jsx)(s.Toaster,{})]})})})}},8549:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>m,tree:()=>d});var o=r(5239),s=r(8088),n=r(8170),i=r.n(n),a=r(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,8014)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=[],u={require:r,loadChunk:()=>Promise.resolve()},m=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[97,423],()=>r(8549));module.exports=o})();