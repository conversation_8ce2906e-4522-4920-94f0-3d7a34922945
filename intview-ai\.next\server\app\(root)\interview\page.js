(()=>{var t={};t.id=812,t.ids=[812],t.modules={846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1079:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>s});let s=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\app\\\\(root)\\\\interview\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\interview\\page.tsx","default")},2379:(t,e,i)=>{Promise.resolve().then(i.bind(i,3308))},3033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3308:(t,e,i)=>{"use strict";let s;i.r(e),i.d(e,{default:()=>r7});var n=i(687),r=i(3210),a=i(4934),o=i(2688);let l=(0,o.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),u=["The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned."],h=["To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face."],d=["Environment Requirements Ensure you are in a quiet, distraction-free space. Sit in a well-lit area so the avatar can see you clearly. Use a stable internet connection and a working camera & microphone .","AI Interview Format Your interviewer will be an AI avatar, speaking and listening in a natural, conversational style. You will respond to 5 preset questions, with roughly under 10 minutes total interview time. You may be gently prompted if your answers run long—please stay within the time suggested .","Recording & Usage This session will be fully recorded (audio & video) for review by our hiring team. Your responses and the recording will be processed by our AI scoring system to evaluate communication, problem-solving, and fit. All data is stored securely and used only for the purposes of hiring this role .","Independence & Integrity Please answer without external aids (notes, websites, or other people). If background noise or interruptions occur, you may be prompted to pause and restart your answer ."],c=({candidateName:t="Jonathan",jobTitle:e="Insurance Agent",languages:i=["English","Chinese"],instructions:s=u,environmentChecklist:o=h,disclaimers:c=d,onNext:p})=>{let[m,f]=(0,r.useState)(!1);return(0,n.jsx)("div",{className:"flex-1 border border-gray-400 rounded-md h-fit bg-white",children:(0,n.jsxs)("div",{className:"p-4 flex flex-col text-[#38383a]",children:[(0,n.jsx)("p",{className:"font-semibold mb-8 text-xl",children:"Instructions for Interview!"}),(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("p",{className:" mb-2 text-md",children:["Hello ",t,"!"]}),(0,n.jsxs)("p",{className:"text-sm mb-4",children:["As part of the process you are required to complete an AI video assessment for the role of the ",e,"."]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Interview Language"}),(0,n.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:i.map((t,e)=>(0,n.jsx)("li",{children:t},e))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Instructions"}),(0,n.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:s.map((t,e)=>(0,n.jsx)("li",{children:t},e))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Environment Checklist:"}),(0,n.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:o.map((t,e)=>(0,n.jsx)("li",{children:t},e))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Important Disclaimers:"}),(0,n.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:c.map((t,e)=>(0,n.jsx)("li",{children:t},e))})]}),(0,n.jsxs)("div",{className:"flex items-start gap-2 mt-6",children:[(0,n.jsx)("input",{type:"checkbox",id:"terms",checked:m,onChange:t=>f(t.target.checked),className:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),(0,n.jsxs)("label",{htmlFor:"terms",className:"text-[11px] text-[#38383a]",children:["By checking this box, you agree with AI Interview"," ",(0,n.jsx)("span",{className:"text-primary cursor-pointer font-medium",children:"Terms of use"}),"."]})]}),(0,n.jsx)("div",{className:"flex justify-center",children:(0,n.jsxs)(a.$,{disabled:!m,variant:"default",size:"lg",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>p&&p(),children:["Proceed",(0,n.jsx)(l,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})})]})]})})},p=(0,o.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);var m=i(659);let f=()=>(0,n.jsx)("div",{className:"bg-white p-4 rounded-2xl shadow-sm mb-6 max-w-xl",children:(0,n.jsxs)("div",{className:"flex justify-between items-start",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-3",children:"UX/UI Designer for Ai-Interview Web App"}),(0,n.jsxs)("div",{className:"flex gap-2 leading-relaxed mb-3 flex-wrap",children:[(0,n.jsxs)("p",{className:"text-sm text-gray-600 font-medium",children:["$500 - $1000 ",(0,n.jsx)("span",{className:"font-extrabold px-1",children:"\xb7"})]}),(0,n.jsxs)("div",{className:"flex gap-1 items-center",children:[(0,n.jsx)(p,{className:"w-4 h-5"}),(0,n.jsx)("p",{className:"text-sm text-gray-600 font-medium",children:"New York"})]}),(0,n.jsxs)("div",{className:"flex gap-1 items-center",children:[(0,n.jsx)(m.A,{className:"w-4 h-5"}),(0,n.jsx)("p",{className:"text-sm text-gray-600 font-medium",children:"Onsite / Remote"})]})]}),(0,n.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"We're building an AI-powered interview tool. We expect you to help users prepare by giving human interview experience generation."})]}),(0,n.jsx)("span",{className:"text-xs bg-[#CCFFB1] text-green-700 px-3 py-1 rounded-full font-medium",children:"Active"})]})}),g=({currentQuestion:t=1,className:e})=>{let i=["Tell us about yourself?","What are your strengths?","Why do you want this job?","Where do you see yourself in 5 years?"];return(0,n.jsxs)("div",{className:`rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] h-[488px] shadow-sm overflow-y-auto scrollbar-hidden ${e||""}`,children:[" ",(0,n.jsx)("h3",{className:"font-semibold text-lg mb-6",children:"Questions"}),(0,n.jsx)("ul",{className:"relative space-y-8  ",children:Array.from({length:4},(e,s)=>(0,n.jsxs)("li",{className:"relative flex items-start space-x-3 mt-4 mb-0 sm:mb-5",children:[3!==s&&(0,n.jsx)("span",{className:"absolute left-[17px] pl-[3px] top-6 mt-11 h-10 w-[3px] rounded-full bg-gradient-to-b from-white to-[#6938EF]"}),(0,n.jsx)("div",{className:`rounded-full w-7 h-7 mt-7 flex items-center p-5 justify-center text-sm font-medium z-10 ${s+1===t?"bg-[#6938EF] text-white":s+1<t?"bg-green-500 text-white":"bg-[#C7ACF5] text-white"}`,children:s+1<t?"✓":s+1}),(0,n.jsx)("span",{className:`text-md font-medium mt-7 ${s+1===t?"text-[#6938EF] font-semibold":"text-[#616161]"}`,children:i[s]})]},s))})]})};var v=i(474);let y={src:"/_next/static/media/avator.608a8af8.png",height:663,width:363,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAICAMAAADp7a43AAAAP1BMVEVobHRET2VocYCsrJ2MlKd7cmNgVVPJ1eeCgXHe5/MYJD6jn613eHJZWmaUnX3Cxc/NxL+PlHhIWnzW3eh8enipbN0SAAAACHRSTlP8/////////goJZaEAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAsSURBVHicBcEHAgAQDACxQ9Ha6/9vlaAaBjHqpqUXOOI8RXzHyVykahfM8gcVIAEOI071+QAAAABJRU5ErkJggg==",blurWidth:4,blurHeight:8},x=({className:t})=>(0,n.jsx)("div",{className:"mt-6 md:mt-0",children:(0,n.jsx)(v.default,{src:y,alt:"Interviewee",className:`rounded-lg object-cover ${t}`,width:300,height:300})}),w=({children:t})=>(0,n.jsx)("div",{className:"border rounded-lg p-6 min-h-[600px] mb-4 flex-1",children:t}),b=({onNext:t})=>(0,n.jsxs)("div",{className:"h-screen",children:[(0,n.jsx)(f,{}),(0,n.jsxs)(w,{children:[(0,n.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start",children:[(0,n.jsx)(g,{className:"h-[550px]"}),(0,n.jsx)(x,{})]}),(0,n.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:(0,n.jsxs)(a.$,{variant:"default",size:"lg",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>t&&t(),children:["Start Interview",(0,n.jsx)(l,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})})]})]}),A=(0,o.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),j=(0,o.A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]),T=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],P=new Set(T),S=t=>180*t/Math.PI,k=t=>E(S(Math.atan2(t[1],t[0]))),C={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:k,rotateZ:k,skewX:t=>S(Math.atan(t[1])),skewY:t=>S(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},E=t=>((t%=360)<0&&(t+=360),t),M=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),V=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),R={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:M,scaleY:V,scale:t=>(M(t)+V(t))/2,rotateX:t=>E(S(Math.atan2(t[6],t[5]))),rotateY:t=>E(S(Math.atan2(-t[2],t[0]))),rotateZ:k,rotate:k,skewX:t=>S(Math.atan(t[4])),skewY:t=>S(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function N(t){return+!!t.includes("scale")}function D(t,e){let i,s;if(!t||"none"===t)return N(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=R,s=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=C,s=e}if(!s)return N(e);let r=i[e],a=s[1].split(",").map(F);return"function"==typeof r?r(a):a[r]}let I=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return D(i,e)};function F(t){return parseFloat(t.trim())}let L=t=>e=>"string"==typeof e&&e.startsWith(t),B=L("--"),O=L("var(--"),U=t=>!!O(t)&&$.test(t.split("/*")[0].trim()),$=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function W({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}let q=(t,e,i)=>t+(e-t)*i;function z(t){return void 0===t||1===t}function Q({scale:t,scaleX:e,scaleY:i}){return!z(t)||!z(e)||!z(i)}function H(t){return Q(t)||X(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function X(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function _(t,e,i,s,n){return void 0!==n&&(t=s+n*(t-s)),s+i*(t-s)+e}function Y(t,e=0,i=1,s,n){t.min=_(t.min,e,i,s,n),t.max=_(t.max,e,i,s,n)}function K(t,{x:e,y:i}){Y(t.x,e.translate,e.scale,e.originPoint),Y(t.y,i.translate,i.scale,i.originPoint)}function G(t,e){t.min=t.min+e,t.max=t.max+e}function Z(t,e,i,s,n=.5){let r=q(t.min,t.max,n);Y(t,e,i,r,s)}function J(t,e){Z(t.x,e.x,e.scaleX,e.scale,e.originX),Z(t.y,e.y,e.scaleY,e.scale,e.originY)}function tt(t,e){return W(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}let te=new Set(["width","height","top","left","right","bottom",...T]),ti=(t,e,i)=>i>e?e:i<t?t:i,ts={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},tn={...ts,transform:t=>ti(0,1,t)},tr={...ts,default:1},ta=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),to=ta("deg"),tl=ta("%"),tu=ta("px"),th=ta("vh"),td=ta("vw"),tc={...tl,parse:t=>tl.parse(t)/100,transform:t=>tl.transform(100*t)},tp=t=>e=>e.test(t),tm=[ts,tu,tl,to,td,th,{test:t=>"auto"===t,parse:t=>t}],tf=t=>tm.find(tp(t)),tg=()=>{},tv=()=>{},ty=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),tx=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,tw=t=>t===ts||t===tu,tb=new Set(["x","y","z"]),tA=T.filter(t=>!tb.has(t)),tj={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>D(e,"x"),y:(t,{transform:e})=>D(e,"y")};tj.translateX=tj.x,tj.translateY=tj.y;let tT=t=>t,tP={},tS=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],tk={value:null,addProjectionMetrics:null};function tC(t,e){let i=!1,s=!0,n={delta:0,timestamp:0,isProcessing:!1},r=()=>i=!0,a=tS.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,s=new Set,n=!1,r=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){a.has(e)&&(h.schedule(e),t()),l++,e(o)}let h={schedule:(t,e=!1,r=!1)=>{let o=r&&n?i:s;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{s.delete(t),a.delete(t)},process:t=>{if(o=t,n){r=!0;return}n=!0,[i,s]=[s,i],i.forEach(u),e&&tk.value&&tk.value.frameloop[e].push(l),l=0,i.clear(),n=!1,r&&(r=!1,h.process(t))}};return h}(r,e?i:void 0),t),{}),{setup:o,read:l,resolveKeyframes:u,preUpdate:h,update:d,preRender:c,render:p,postRender:m}=a,f=()=>{let r=tP.useManualTiming?n.timestamp:performance.now();i=!1,tP.useManualTiming||(n.delta=s?1e3/60:Math.max(Math.min(r-n.timestamp,40),1)),n.timestamp=r,n.isProcessing=!0,o.process(n),l.process(n),u.process(n),h.process(n),d.process(n),c.process(n),p.process(n),m.process(n),n.isProcessing=!1,i&&e&&(s=!1,t(f))},g=()=>{i=!0,s=!0,n.isProcessing||t(f)};return{schedule:tS.reduce((t,e)=>{let s=a[e];return t[e]=(t,e=!1,n=!1)=>(i||g(),s.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<tS.length;e++)a[tS[e]].cancel(t)},state:n,steps:a}}let{schedule:tE,cancel:tM,state:tV,steps:tR}=tC("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:tT,!0),tN=new Set,tD=!1,tI=!1,tF=!1;function tL(){if(tI){let t=Array.from(tN).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return tA.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}tI=!1,tD=!1,tN.forEach(t=>t.complete(tF)),tN.clear()}function tB(){tN.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(tI=!0)})}class tO{constructor(t,e,i,s,n,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=n,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(tN.add(this),tD||(tD=!0,tE.read(tB),tE.resolveKeyframes(tL))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;if(null===t[0]){let n=s?.get(),r=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let s=i.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===n&&s.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),tN.delete(this)}cancel(){"scheduled"===this.state&&(tN.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tU=t=>/^0[^.\s]+$/u.test(t),t$=t=>Math.round(1e5*t)/1e5,tW=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tq=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tz=(t,e)=>i=>!!("string"==typeof i&&tq.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tQ=(t,e,i)=>s=>{if("string"!=typeof s)return s;let[n,r,a,o]=s.match(tW);return{[t]:parseFloat(n),[e]:parseFloat(r),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},tH=t=>ti(0,255,t),tX={...ts,transform:t=>Math.round(tH(t))},t_={test:tz("rgb","red"),parse:tQ("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+tX.transform(t)+", "+tX.transform(e)+", "+tX.transform(i)+", "+t$(tn.transform(s))+")"},tY={test:tz("#"),parse:function(t){let e="",i="",s="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,s+=s,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:n?parseInt(n,16)/255:1}},transform:t_.transform},tK={test:tz("hsl","hue"),parse:tQ("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:s=1})=>"hsla("+Math.round(t)+", "+tl.transform(t$(e))+", "+tl.transform(t$(i))+", "+t$(tn.transform(s))+")"},tG={test:t=>t_.test(t)||tY.test(t)||tK.test(t),parse:t=>t_.test(t)?t_.parse(t):tK.test(t)?tK.parse(t):tY.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?t_.transform(t):tK.transform(t),getAnimatableNone:t=>{let e=tG.parse(t);return e.alpha=0,tG.transform(e)}},tZ=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tJ="number",t0="color",t1=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function t2(t){let e=t.toString(),i=[],s={color:[],number:[],var:[]},n=[],r=0,a=e.replace(t1,t=>(tG.test(t)?(s.color.push(r),n.push(t0),i.push(tG.parse(t))):t.startsWith("var(")?(s.var.push(r),n.push("var"),i.push(t)):(s.number.push(r),n.push(tJ),i.push(parseFloat(t))),++r,"${}")).split("${}");return{values:i,split:a,indexes:s,types:n}}function t3(t){return t2(t).values}function t5(t){let{split:e,types:i}=t2(t),s=e.length;return t=>{let n="";for(let r=0;r<s;r++)if(n+=e[r],void 0!==t[r]){let e=i[r];e===tJ?n+=t$(t[r]):e===t0?n+=tG.transform(t[r]):n+=t[r]}return n}}let t4=t=>"number"==typeof t?0:tG.test(t)?tG.getAnimatableNone(t):t,t6={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(tW)?.length||0)+(t.match(tZ)?.length||0)>0},parse:t3,createTransformer:t5,getAnimatableNone:function(t){let e=t3(t);return t5(t)(e.map(t4))}},t9=new Set(["brightness","contrast","saturate","opacity"]);function t8(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(tW)||[];if(!s)return t;let n=i.replace(s,""),r=+!!t9.has(e);return s!==i&&(r*=100),e+"("+r+n+")"}let t7=/\b([a-z-]*)\(.*?\)/gu,et={...t6,getAnimatableNone:t=>{let e=t.match(t7);return e?e.map(t8).join(" "):t}},ee={...ts,transform:Math.round},ei={borderWidth:tu,borderTopWidth:tu,borderRightWidth:tu,borderBottomWidth:tu,borderLeftWidth:tu,borderRadius:tu,radius:tu,borderTopLeftRadius:tu,borderTopRightRadius:tu,borderBottomRightRadius:tu,borderBottomLeftRadius:tu,width:tu,maxWidth:tu,height:tu,maxHeight:tu,top:tu,right:tu,bottom:tu,left:tu,padding:tu,paddingTop:tu,paddingRight:tu,paddingBottom:tu,paddingLeft:tu,margin:tu,marginTop:tu,marginRight:tu,marginBottom:tu,marginLeft:tu,backgroundPositionX:tu,backgroundPositionY:tu,rotate:to,rotateX:to,rotateY:to,rotateZ:to,scale:tr,scaleX:tr,scaleY:tr,scaleZ:tr,skew:to,skewX:to,skewY:to,distance:tu,translateX:tu,translateY:tu,translateZ:tu,x:tu,y:tu,z:tu,perspective:tu,transformPerspective:tu,opacity:tn,originX:tc,originY:tc,originZ:tu,zIndex:ee,fillOpacity:tn,strokeOpacity:tn,numOctaves:ee},es={...ei,color:tG,backgroundColor:tG,outlineColor:tG,fill:tG,stroke:tG,borderColor:tG,borderTopColor:tG,borderRightColor:tG,borderBottomColor:tG,borderLeftColor:tG,filter:et,WebkitFilter:et},en=t=>es[t];function er(t,e){let i=en(t);return i!==et&&(i=t6),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let ea=new Set(["auto","none","0"]);class eo extends tO{constructor(t,e,i,s,n){super(t,e,i,s,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&U(s=s.trim())){let n=function t(e,i,s=1){tv(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[n,r]=function(t){let e=tx.exec(t);if(!e)return[,];let[,i,s,n]=e;return[`--${i??s}`,n]}(e);if(!n)return;let a=window.getComputedStyle(i).getPropertyValue(n);if(a){let t=a.trim();return ty(t)?parseFloat(t):t}return U(r)?t(r,i,s+1):r}(s,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!te.has(i)||2!==t.length)return;let[s,n]=t,r=tf(s),a=tf(n);if(r!==a)if(tw(r)&&tw(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else tj[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;(null===t[e]||("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||tU(s)))&&i.push(e)}i.length&&function(t,e,i){let s,n=0;for(;n<t.length&&!s;){let e=t[n];"string"==typeof e&&!ea.has(e)&&t2(e).values.length&&(s=t[n]),n++}if(s&&i)for(let n of e)t[n]=er(i,s)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tj[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);let n=i.length-1,r=i[n];i[n]=tj[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let el=t=>!!(t&&t.getVelocity);function eu(){s=void 0}let eh={now:()=>(void 0===s&&eh.set(tV.isProcessing||tP.useManualTiming?tV.timestamp:performance.now()),s),set:t=>{s=t,queueMicrotask(eu)}};function ed(t,e){-1===t.indexOf(e)&&t.push(e)}function ec(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class ep{constructor(){this.subscriptions=[]}add(t){return ed(this.subscriptions,t),()=>ec(this.subscriptions,t)}notify(t,e,i){let s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,i);else for(let n=0;n<s;n++){let s=this.subscriptions[n];s&&s(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let em=t=>!isNaN(parseFloat(t)),ef={current:void 0};class eg{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=t=>{let e=eh.now();if(this.updatedAt!==e&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=eh.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=em(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new ep);let i=this.events[t].add(e);return"change"===t?()=>{i(),tE.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return ef.current&&ef.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=eh.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ev(t,e){return new eg(t,e)}let ey=[...tm,tG,t6],ex=t=>ey.find(tp(t)),{schedule:ew}=tC(queueMicrotask,!1),eb={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},eA={};for(let t in eb)eA[t]={isEnabled:e=>eb[t].some(t=>!!e[t])};let ej=()=>({translate:0,scale:1,origin:0,originPoint:0}),eT=()=>({x:ej(),y:ej()}),eP=()=>({min:0,max:0}),eS=()=>({x:eP(),y:eP()}),ek="undefined"!=typeof window,eC={current:null},eE={current:!1},eM=new WeakMap;function eV(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function eR(t){return"string"==typeof t||Array.isArray(t)}let eN=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],eD=["initial",...eN];function eI(t){return eV(t.animate)||eD.some(e=>eR(t[e]))}function eF(t){return!!(eI(t)||t.variants)}function eL(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function eB(t,e,i,s){if("function"==typeof e){let[n,r]=eL(s);e=e(void 0!==i?i:t.custom,n,r)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,r]=eL(s);e=e(void 0!==i?i:t.custom,n,r)}return e}let eO=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class eU{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:n,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tO,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=eh.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,tE.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=r;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!n,this.isControllingVariants=eI(e),this.isVariantNode=eF(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==o[t]&&el(e)&&e.set(o[t])}}mount(t){this.current=t,eM.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),eE.current||function(){if(eE.current=!0,ek)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>eC.current=t.matches;t.addEventListener("change",e),e()}else eC.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||eC.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),tM(this.notifyUpdate),tM(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}addChild(t){this.children.add(t),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(t)}removeChild(t){this.children.delete(t),this.enteringChildren&&this.enteringChildren.delete(t)}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=P.has(t);s&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&tE.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in eA){let e=eA[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):eS()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<eO.length;e++){let i=eO[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let n=e[s],r=i[s];if(el(n))t.addValue(s,n);else if(el(r))t.addValue(s,ev(n,{owner:t}));else if(r!==n)if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(s);t.addValue(s,ev(void 0!==e?e:n,{owner:t}))}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=ev(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(ty(i)||tU(i))?i=parseFloat(i):!ex(i)&&t6.test(e)&&(i=er(t,e)),this.setBaseTarget(t,el(i)?i.get():i)),el(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let s=eB(this.props,i,this.presenceContext?.custom);s&&(e=s[t])}if(i&&void 0!==e)return e;let s=this.getBaseTargetFromProps(this.props,t);return void 0===s||el(s)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new ep),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}scheduleRenderMicrotask(){ew.render(this.render)}}class e$ extends eU{constructor(){super(...arguments),this.KeyframeResolver=eo}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;el(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}let eW=(t,e)=>e&&"number"==typeof t?e.transform(t):t,eq={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ez=T.length;function eQ(t,e,i){let{style:s,vars:n,transformOrigin:r}=t,a=!1,o=!1;for(let t in e){let i=e[t];if(P.has(t)){a=!0;continue}if(B(t)){n[t]=i;continue}{let e=eW(i,ei[t]);t.startsWith("origin")?(o=!0,r[t]=e):s[t]=e}}if(!e.transform&&(a||i?s.transform=function(t,e,i){let s="",n=!0;for(let r=0;r<ez;r++){let a=T[r],o=t[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let t=eW(o,ei[a]);if(!l){n=!1;let e=eq[a]||a;s+=`${e}(${t}) `}i&&(e[a]=t)}}return s=s.trim(),i?s=i(e,n?"":s):n&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),o){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;s.transformOrigin=`${t} ${e} ${i}`}}function eH(t,{style:e,vars:i},s,n){let r,a=t.style;for(r in e)a[r]=e[r];for(r in n?.applyProjectionStyles(a,s),i)a.setProperty(r,i[r])}let eX={};function e_(t,{layout:e,layoutId:i}){return P.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!eX[t]||"opacity"===t)}function eY(t,e,i){let{style:s}=t,n={};for(let r in s)(el(s[r])||e.style&&el(e.style[r])||e_(r,t)||i?.getValue(r)?.liveStyle!==void 0)&&(n[r]=s[r]);return n}class eK extends e${constructor(){super(...arguments),this.type="html",this.renderInstance=eH}readValueFromInstance(t,e){if(P.has(e))return this.projection?.isProjecting?N(e):I(t,e);{let i=window.getComputedStyle(t),s=(B(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return tt(t,e)}build(t,e,i){eQ(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return eY(t,e,i)}}let eG=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),eZ={offset:"stroke-dashoffset",array:"stroke-dasharray"},eJ={offset:"strokeDashoffset",array:"strokeDasharray"};function e0(t,{attrX:e,attrY:i,attrScale:s,pathLength:n,pathSpacing:r=1,pathOffset:a=0,...o},l,u,h){if(eQ(t,o,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=h?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==s&&(d.scale=s),void 0!==n&&function(t,e,i=1,s=0,n=!0){t.pathLength=1;let r=n?eZ:eJ;t[r.offset]=tu.transform(-s);let a=tu.transform(e),o=tu.transform(i);t[r.array]=`${a} ${o}`}(d,n,r,a,!1)}let e1=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),e2=t=>"string"==typeof t&&"svg"===t.toLowerCase();function e3(t,e,i){let s=eY(t,e,i);for(let i in t)(el(t[i])||el(e[i]))&&(s[-1!==T.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}class e5 extends e${constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=eS}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(P.has(e)){let t=en(e);return t&&t.default||0}return e=e1.has(e)?e:eG(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return e3(t,e,i)}build(t,e,i){e0(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,s){for(let i in eH(t,e,void 0,s),e.attrs)t.setAttribute(e1.has(i)?i:eG(i),e.attrs[i])}mount(t){this.isSVGTag=e2(t.tagName),super.mount(t)}}let e4=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function e6(t){if("string"!=typeof t||t.includes("-"));else if(e4.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}let e9=(0,r.createContext)({}),e8=(0,r.createContext)({strict:!1}),e7=(0,r.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),it=(0,r.createContext)({});function ie(t){return Array.isArray(t)?t.join(" "):t}let ii=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function is(t,e,i){for(let s in e)el(e[s])||e_(s,i)||(t[s]=e[s])}let ir=()=>({...ii(),attrs:{}}),ia=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function io(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||ia.has(t)}let il=t=>!io(t);try{!function(t){"function"==typeof t&&(il=e=>e.startsWith("on")?!io(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let iu=(0,r.createContext)(null);function ih(t){return el(t)?t.get():t}let id=t=>(e,i)=>{let s=(0,r.useContext)(it),n=(0,r.useContext)(iu),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,s,n){return{latestValues:function(t,e,i,s){let n={},r=s(t,{});for(let t in r)n[t]=ih(r[t]);let{initial:a,animate:o}=t,l=eI(t),u=eF(t);e&&u&&!l&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===o&&(o=e.animate));let h=!!i&&!1===i.initial,d=(h=h||!1===a)?o:a;if(d&&"boolean"!=typeof d&&!eV(d)){let e=Array.isArray(d)?d:[d];for(let i=0;i<e.length;i++){let s=eB(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=h?e.length-1:0;e=e[t]}null!==e&&(n[t]=e)}for(let e in t)n[e]=t[e]}}}return n}(i,s,n,t),renderState:e()}})(t,e,s,n);return i?a():function(t){let e=(0,r.useRef)(null);return null===e.current&&(e.current=t()),e.current}(a)},ic=id({scrapeMotionValuesFromProps:eY,createRenderState:ii}),ip=id({scrapeMotionValuesFromProps:e3,createRenderState:ir}),im=Symbol.for("motionComponentSymbol");function ig(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iv="data-"+eG("framerAppearId"),iy=(0,r.createContext)({}),ix=ek?r.useLayoutEffect:r.useEffect;function iw(t,{forwardMotionProps:e=!1}={},i,s){i&&function(t){for(let e in t)eA[e]={...eA[e],...t[e]}}(i);let a=e6(t)?ip:ic;function o(i,o){var l,u,h;let d,c={...(0,r.useContext)(e7),...i,layoutId:function({layoutId:t}){let e=(0,r.useContext)(e9).id;return e&&void 0!==t?e+"-"+t:t}(i)},{isStatic:p}=c,m=function(t){let{initial:e,animate:i}=function(t,e){if(eI(t)){let{initial:e,animate:i}=t;return{initial:!1===e||eR(e)?e:void 0,animate:eR(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,r.useContext)(it));return(0,r.useMemo)(()=>({initial:e,animate:i}),[ie(e),ie(i)])}(i),f=a(i,p);if(!p&&ek){u=0,h=0,(0,r.useContext)(e8).strict;let e=function(t){let{drag:e,layout:i}=eA;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:e?.isEnabled(t)||i?.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(c);d=e.MeasureLayout,m.visualElement=function(t,e,i,s,n){let{visualElement:a}=(0,r.useContext)(it),o=(0,r.useContext)(e8),l=(0,r.useContext)(iu),u=(0,r.useContext)(e7).reducedMotion,h=(0,r.useRef)(null);s=s||o.renderer,!h.current&&s&&(h.current=s(t,{visualState:e,parent:a,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));let d=h.current,c=(0,r.useContext)(iy);d&&!d.projection&&n&&("html"===d.type||"svg"===d.type)&&function(t,e,i,s){let{layoutId:n,layout:r,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:r,alwaysMeasureLayout:!!a||o&&ig(o),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:s,crossfade:h,layoutScroll:l,layoutRoot:u})}(h.current,i,n,c);let p=(0,r.useRef)(!1);(0,r.useInsertionEffect)(()=>{d&&p.current&&d.update(i,l)});let m=i[iv],f=(0,r.useRef)(!!m&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return ix(()=>{d&&(p.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),d.scheduleRenderMicrotask(),f.current&&d.animationState&&d.animationState.animateChanges())}),(0,r.useEffect)(()=>{d&&(!f.current&&d.animationState&&d.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),f.current=!1),d.enteringChildren=void 0)}),d}(t,f,c,s,e.ProjectionNode)}return(0,n.jsxs)(it.Provider,{value:m,children:[d&&m.visualElement?(0,n.jsx)(d,{visualElement:m.visualElement,...c}):null,function(t,e,i,{latestValues:s},n,a=!1){let o=(e6(t)?function(t,e,i,s){let n=(0,r.useMemo)(()=>{let i=ir();return e0(i,e,e2(s),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};is(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return is(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,r.useMemo)(()=>{let i=ii();return eQ(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(e,s,n,t),l=function(t,e,i){let s={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(il(n)||!0===i&&io(n)||!e&&!io(n)||t.draggable&&n.startsWith("onDrag"))&&(s[n]=t[n]);return s}(e,"string"==typeof t,a),u=t!==r.Fragment?{...l,...o,ref:i}:{},{children:h}=e,d=(0,r.useMemo)(()=>el(h)?h.get():h,[h]);return(0,r.createElement)(t,{...u,children:d})}(t,i,(l=m.visualElement,(0,r.useCallback)(t=>{t&&f.onMount&&f.onMount(t),l&&(t?l.mount(t):l.unmount()),o&&("function"==typeof o?o(t):ig(o)&&(o.current=t))},[l])),f,p,e)]})}o.displayName=`motion.${"string"==typeof t?t:`create(${t.displayName??t.name??""})`}`;let l=(0,r.forwardRef)(o);return l[im]=t,l}function ib(t,e,i){let s=t.getProps();return eB(s,e,void 0!==i?i:s.custom,t)}function iA(t,e){return t?.[e]??t?.default??t}let ij=t=>Array.isArray(t);function iT(t,e){let i=t.getValue("willChange");if(el(i)&&i.add)return i.add(e);if(!i&&tP.WillChange){let i=new tP.WillChange("auto");t.addValue("willChange",i),i.add(e)}}function iP(t){t.duration=0,t.type}let iS=(t,e)=>i=>e(t(i)),ik=(...t)=>t.reduce(iS),iC=t=>1e3*t,iE=t=>t/1e3,iM={layout:0,mainThread:0,waapi:0};function iV(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function iR(t,e){return i=>i>0?e:t}let iN=(t,e,i)=>{let s=t*t,n=i*(e*e-s)+s;return n<0?0:Math.sqrt(n)},iD=[tY,t_,tK],iI=t=>iD.find(e=>e.test(t));function iF(t){let e=iI(t);if(tg(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!e)return!1;let i=e.parse(t);return e===tK&&(i=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let n=0,r=0,a=0;if(e/=100){let s=i<.5?i*(1+e):i+e-i*e,o=2*i-s;n=iV(o,s,t+1/3),r=iV(o,s,t),a=iV(o,s,t-1/3)}else n=r=a=i;return{red:Math.round(255*n),green:Math.round(255*r),blue:Math.round(255*a),alpha:s}}(i)),i}let iL=(t,e)=>{let i=iF(t),s=iF(e);if(!i||!s)return iR(t,e);let n={...i};return t=>(n.red=iN(i.red,s.red,t),n.green=iN(i.green,s.green,t),n.blue=iN(i.blue,s.blue,t),n.alpha=q(i.alpha,s.alpha,t),t_.transform(n))},iB=new Set(["none","hidden"]);function iO(t,e){return i=>q(t,e,i)}function iU(t){return"number"==typeof t?iO:"string"==typeof t?U(t)?iR:tG.test(t)?iL:iq:Array.isArray(t)?i$:"object"==typeof t?tG.test(t)?iL:iW:iR}function i$(t,e){let i=[...t],s=i.length,n=t.map((t,i)=>iU(t)(t,e[i]));return t=>{for(let e=0;e<s;e++)i[e]=n[e](t);return i}}function iW(t,e){let i={...t,...e},s={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(s[n]=iU(t[n])(t[n],e[n]));return t=>{for(let e in s)i[e]=s[e](t);return i}}let iq=(t,e)=>{let i=t6.createTransformer(e),s=t2(t),n=t2(e);return s.indexes.var.length===n.indexes.var.length&&s.indexes.color.length===n.indexes.color.length&&s.indexes.number.length>=n.indexes.number.length?iB.has(t)&&!n.values.length||iB.has(e)&&!s.values.length?function(t,e){return iB.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):ik(i$(function(t,e){let i=[],s={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let r=e.types[n],a=t.indexes[r][s[r]],o=t.values[a]??0;i[n]=o,s[r]++}return i}(s,n),n.values),i):(tg(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),iR(t,e))};function iz(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?q(t,e,i):iU(t)(t,e)}let iQ=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>tE.update(e,t),stop:()=>tM(e),now:()=>tV.isProcessing?tV.timestamp:eh.now()}},iH=(t,e,i=10)=>{let s="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)s+=Math.round(1e4*t(e/(n-1)))/1e4+", ";return`linear(${s.substring(0,s.length-2)})`};function iX(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function i_(t,e,i){var s,n;let r=Math.max(e-5,0);return s=i-t(r),(n=e-r)?1e3/n*s:0}let iY={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function iK(t,e){return t*Math.sqrt(1-e*e)}let iG=["duration","bounce"],iZ=["stiffness","damping","mass"];function iJ(t,e){return e.some(e=>void 0!==t[e])}function i0(t=iY.visualDuration,e=iY.bounce){let i,s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:r}=s,a=s.keyframes[0],o=s.keyframes[s.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:iY.velocity,stiffness:iY.stiffness,damping:iY.damping,mass:iY.mass,isResolvedFromDuration:!1,...t};if(!iJ(t,iZ)&&iJ(t,iG))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,n=2*ti(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:iY.mass,stiffness:s,damping:n}}else{let i=function({duration:t=iY.duration,bounce:e=iY.bounce,velocity:i=iY.velocity,mass:s=iY.mass}){let n,r;tg(t<=iC(iY.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let a=1-e;a=ti(iY.minDamping,iY.maxDamping,a),t=ti(iY.minDuration,iY.maxDuration,iE(t)),a<1?(n=e=>{let s=e*a,n=s*t;return .001-(s-i)/iK(e,a)*Math.exp(-n)},r=e=>{let s=e*a*t,r=Math.pow(a,2)*Math.pow(e,2)*t,o=Math.exp(-s),l=iK(Math.pow(e,2),a);return(s*i+i-r)*o*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let o=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(n,r,5/t);if(t=iC(t),isNaN(o))return{stiffness:iY.stiffness,damping:iY.damping,duration:t};{let e=Math.pow(o,2)*s;return{stiffness:e,damping:2*a*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:iY.mass}).isResolvedFromDuration=!0}return e}({...s,velocity:-iE(s.velocity||0)}),f=p||0,g=h/(2*Math.sqrt(u*d)),v=o-a,y=iE(Math.sqrt(u/d)),x=5>Math.abs(v);if(n||(n=x?iY.restSpeed.granular:iY.restSpeed.default),r||(r=x?iY.restDelta.granular:iY.restDelta.default),g<1){let t=iK(y,g);i=e=>o-Math.exp(-g*y*e)*((f+g*y*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}else if(1===g)i=t=>o-Math.exp(-y*t)*(v+(f+y*v)*t);else{let t=y*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*y*e),s=Math.min(t*e,300);return o-i*((f+g*y*v)*Math.sinh(s)+t*v*Math.cosh(s))/t}}let w={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let s=0===t?f:0;g<1&&(s=0===t?iC(f):i_(i,t,e));let a=Math.abs(o-e)<=r;l.done=Math.abs(s)<=n&&a}return l.value=l.done?o:e,l},toString:()=>{let t=Math.min(iX(w),2e4),e=iH(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function i1({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:n=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:h}){let d,c,p=t[0],m={done:!1,value:p},f=t=>void 0!==o&&t<o||void 0!==l&&t>l,g=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l,v=i*e,y=p+v,x=void 0===a?y:a(y);x!==y&&(v=x-p);let w=t=>-v*Math.exp(-t/s),b=t=>x+w(t),A=t=>{let e=w(t),i=b(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},j=t=>{f(m.value)&&(d=t,c=i0({keyframes:[m.value,g(m.value)],velocity:i_(b,t,m.value),damping:n,stiffness:r,restDelta:u,restSpeed:h}))};return j(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,A(t),j(t)),void 0!==d&&t>=d)?c.next(t-d):(e||A(t),m)}}}i0.applyToOptions=t=>{let e=function(t,e=100,i){let s=i({...t,keyframes:[0,e]}),n=Math.min(iX(s),2e4);return{type:"keyframes",ease:t=>s.next(n*t).value/e,duration:iE(n)}}(t,100,i0);return t.ease=e.ease,t.duration=iC(e.duration),t.type="keyframes",t};let i2=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function i3(t,e,i,s){if(t===e&&i===s)return tT;let n=e=>(function(t,e,i,s,n){let r,a,o=0;do(r=i2(a=e+(i-e)/2,s,n)-t)>0?i=a:e=a;while(Math.abs(r)>1e-7&&++o<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:i2(n(t),e,s)}let i5=i3(.42,0,1,1),i4=i3(0,0,.58,1),i6=i3(.42,0,.58,1),i9=t=>Array.isArray(t)&&"number"!=typeof t[0],i8=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,i7=t=>e=>1-t(1-e),st=i3(.33,1.53,.69,.99),se=i7(st),si=i8(se),ss=t=>(t*=2)<1?.5*se(t):.5*(2-Math.pow(2,-10*(t-1))),sn=t=>1-Math.sin(Math.acos(t)),sr=i7(sn),sa=i8(sn),so=t=>Array.isArray(t)&&"number"==typeof t[0],sl={linear:tT,easeIn:i5,easeInOut:i6,easeOut:i4,circIn:sn,circInOut:sa,circOut:sr,backIn:se,backInOut:si,backOut:st,anticipate:ss},su=t=>"string"==typeof t,sh=t=>{if(so(t)){tv(4===t.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[e,i,s,n]=t;return i3(e,i,s,n)}return su(t)?(tv(void 0!==sl[t],`Invalid easing type '${t}'`,"invalid-easing-type"),sl[t]):t},sd=(t,e,i)=>{let s=e-t;return 0===s?1:(i-t)/s};function sc({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){var n;let r=i9(s)?s.map(sh):sh(s),a={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:s,mixer:n}={}){let r=t.length;if(tv(r===e.length,"Both input and output ranges must be the same length","range-length"),1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];let a=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,i){let s=[],n=i||tP.mix||iz,r=t.length-1;for(let i=0;i<r;i++){let r=n(t[i],t[i+1]);e&&(r=ik(Array.isArray(e)?e[i]||tT:e,r)),s.push(r)}return s}(e,s,n),l=o.length,u=i=>{if(a&&i<t[0])return e[0];let s=0;if(l>1)for(;s<t.length-2&&!(i<t[s+1]);s++);let n=sd(t[s],t[s+1],i);return o[s](n)};return i?e=>u(ti(t[0],t[r-1],e)):u}((n=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let n=sd(0,e,s);t.push(q(i,1,n))}}(e,t.length-1),e}(e),n.map(e=>e*t)),e,{ease:Array.isArray(r)?r:e.map(()=>r||i6).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(a.value=o(e),a.done=e>=t,a)}}let sp=t=>null!==t;function sm(t,{repeat:e,repeatType:i="loop"},s,n=1){let r=t.filter(sp),a=n<0||e&&"loop"!==i&&e%2==1?0:r.length-1;return a&&void 0!==s?s:r[a]}let sf={decay:i1,inertia:i1,tween:sc,keyframes:sc,spring:i0};function sg(t){"string"==typeof t.type&&(t.type=sf[t.type])}class sv{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let sy=t=>t/100;class sx extends sv{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==eh.now()&&this.tick(eh.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},iM.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;sg(t);let{type:e=sc,repeat:i=0,repeatDelay:s=0,repeatType:n,velocity:r=0}=t,{keyframes:a}=t,o=e||sc;o!==sc&&"number"!=typeof a[0]&&(this.mixKeyframes=ik(sy,iz(a[0],a[1])),a=[0,100]);let l=o({...t,keyframes:a});"mirror"===n&&(this.mirroredGenerator=o({...t,keyframes:[...a].reverse(),velocity:-r})),null===l.calculatedDuration&&(l.calculatedDuration=iX(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:s,mixKeyframes:n,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>s;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let y=this.currentTime,x=i;if(h){let t=Math.min(this.currentTime,s)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/a)):"mirror"===d&&(x=r)),y=ti(0,1,i)*a}let w=v?{done:!1,value:u[0]}:x.next(y);n&&(w.value=n(w.value));let{done:b}=w;v||null===o||(b=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);let A=null===this.holdTime&&("finished"===this.state||"running"===this.state&&b);return A&&p!==i1&&(w.value=sm(u,this.options,f,this.speed)),m&&m(w.value),A&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return iE(this.calculatedDuration)}get time(){return iE(this.currentTime)}set time(t){t=iC(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(eh.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=iE(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=iQ,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(eh.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,iM.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let sw=t=>t.startsWith("--");function sb(t){let e;return()=>(void 0===e&&(e=t()),e)}let sA=sb(()=>void 0!==window.ScrollTimeline),sj={},sT=function(t,e){let i=sb(t);return()=>sj[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),sP=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,sS={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:sP([0,.65,.55,1]),circOut:sP([.55,0,1,.45]),backIn:sP([.31,.01,.66,-.59]),backOut:sP([.33,1.53,.69,.99])};function sk(t){return"function"==typeof t&&"applyToOptions"in t}class sC extends sv{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:s,pseudoElement:n,allowFlatten:r=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=!!n,this.allowFlatten=r,this.options=t,tv("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:t,...e}){return sk(t)&&sT()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:s=0,duration:n=300,repeat:r=0,repeatType:a="loop",ease:o="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?sT()?iH(e,i):"ease-out":so(e)?sP(e):Array.isArray(e)?e.map(e=>t(e,i)||sS.easeOut):sS[e]}(o,n);Array.isArray(d)&&(h.easing=d),tk.value&&iM.waapi++;let c={delay:s,duration:n,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:r+1,direction:"reverse"===a?"alternate":"normal"};u&&(c.pseudoElement=u);let p=t.animate(h,c);return tk.value&&p.finished.finally(()=>{iM.waapi--}),p}(e,i,s,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=sm(s,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){sw(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return iE(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return iE(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=iC(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&sA())?(this.animation.timeline=t,tT):e(this)}}let sE={anticipate:ss,backInOut:si,circInOut:sa};class sM extends sC{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in sE&&(t.ease=sE[t.ease])}(t),sg(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:s,element:n,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let a=new sx({...r,autoplay:!1}),o=iC(this.finishedTime??this.time);e.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}let sV=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(t6.test(t)||"0"===t)&&!t.startsWith("url(")),sR=new Set(["opacity","clipPath","filter","transform"]),sN=sb(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class sD extends sv{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:r="loop",keyframes:a,name:o,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=eh.now();let d={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:n,repeatType:r,name:o,motionValue:l,element:u,...h},c=u?.KeyframeResolver||tO;this.keyframeResolver=new c(a,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,s){this.keyframeResolver=void 0;let{name:n,type:r,velocity:a,delay:o,isHandoff:l,onUpdate:u}=i;this.resolvedAt=eh.now(),!function(t,e,i,s){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],a=sV(n,e),o=sV(r,e);return tg(a===o,`You are trying to animate ${e} from "${n}" to "${r}". "${a?r:n}" is not an animatable value.`,"value-not-animatable"),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||sk(i))&&s)}(t,n,r,a)&&((tP.instantAnimations||!o)&&u?.(sm(t,i,e)),t[0]=t[t.length-1],iP(i),i.repeat=0);let h={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:s,repeatType:n,damping:r,type:a}=t;if(!(e?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return sN()&&i&&sR.has(i)&&("transform"!==i||!l)&&!o&&!s&&"mirror"!==n&&0!==r&&"inertia"!==a}(h)?new sM({...h,element:h.motionValue.owner.current}):new sx(h);d.finished.then(()=>this.notifyFinished()).catch(tT),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tF=!0,tB(),tL(),tF=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let sI=t=>null!==t,sF={type:"spring",stiffness:500,damping:25,restSpeed:10},sL=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),sB={type:"keyframes",duration:.8},sO={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},sU=(t,{keyframes:e})=>e.length>2?sB:P.has(t)?t.startsWith("scale")?sL(e[1]):sF:sO,s$=(t,e,i,s={},n,r)=>a=>{let o=iA(s,t)||{},l=o.delay||s.delay||0,{elapsed:u=0}=s;u-=iC(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...o,delay:-u,onUpdate:t=>{e.set(t),o.onUpdate&&o.onUpdate(t)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:t,motionValue:e,element:r?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:n,repeat:r,repeatType:a,repeatDelay:o,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(o)&&Object.assign(h,sU(t,h)),h.duration&&(h.duration=iC(h.duration)),h.repeatDelay&&(h.repeatDelay=iC(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(iP(h),0===h.delay&&(d=!0)),(tP.instantAnimations||tP.skipAnimations)&&(d=!0,iP(h),h.delay=0),h.allowFlatten=!o.type&&!o.ease,d&&!r&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},s){let n=t.filter(sI),r=e&&"loop"!==i&&e%2==1?0:n.length-1;return n[r]}(h.keyframes,o);if(void 0!==t)return void tE.update(()=>{h.onUpdate(t),h.onComplete()})}return o.isSync?new sx(h):new sD(h)};function sW(t,e,{delay:i=0,transitionOverride:s,type:n}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:a,...o}=e;s&&(r=s);let l=[],u=n&&t.animationState&&t.animationState.getState()[n];for(let e in o){let s=t.getValue(e,t.latestValues[e]??null),n=o[e];if(void 0===n||u&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(u,e))continue;let a={delay:i,...iA(r||{},e)},h=s.get();if(void 0!==h&&!s.isAnimating&&!Array.isArray(n)&&n===h&&!a.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let i=t.props[iv];if(i){let t=window.MotionHandoffAnimation(i,e,tE);null!==t&&(a.startTime=t,d=!0)}}iT(t,e),s.start(s$(e,s,n,t.shouldReduceMotion&&te.has(e)?{type:!1}:a,t,d));let c=s.animation;c&&l.push(c)}return a&&Promise.all(l).then(()=>{tE.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:s={},...n}=ib(t,e)||{};for(let e in n={...n,...i}){var r;let i=ij(r=n[e])?r[r.length-1]||0:r;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,ev(i))}}(t,a)})}),l}function sq(t,e,i,s=0,n=1){let r=Array.from(t).sort((t,e)=>t.sortNodePosition(e)).indexOf(e),a=t.size,o=(a-1)*s;return"function"==typeof i?i(r,a):1===n?r*s:o-r*s}function sz(t,e,i={}){let s=ib(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=s||{};i.transitionOverride&&(n=i.transitionOverride);let r=s?()=>Promise.all(sW(t,s,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:r=0,staggerChildren:a,staggerDirection:o}=n;return function(t,e,i=0,s=0,n=0,r=1,a){let o=[];for(let l of t.variantChildren)l.notify("AnimationStart",e),o.push(sz(l,e,{...a,delay:i+("function"==typeof s?0:s)+sq(t.variantChildren,l,s,n,r)}).then(()=>l.notify("AnimationComplete",e)));return Promise.all(o)}(t,e,s,r,a,o,i)}:()=>Promise.resolve(),{when:o}=n;if(!o)return Promise.all([r(),a(i.delay)]);{let[t,e]="beforeChildren"===o?[r,a]:[a,r];return t().then(()=>e())}}function sQ(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}let sH=eD.length,sX=[...eN].reverse(),s_=eN.length;function sY(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function sK(){return{animate:sY(!0),whileInView:sY(),whileHover:sY(),whileTap:sY(),whileDrag:sY(),whileFocus:sY(),exit:sY()}}class sG{constructor(t){this.isMounted=!1,this.node=t}update(){}}class sZ extends sG{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>sz(t,e,i)));else if("string"==typeof e)s=sz(t,e,i);else{let n="function"==typeof e?ib(t,e,i.custom):e;s=Promise.all(sW(t,n,i))}return s.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=sK(),s=!0,n=e=>(i,s)=>{let n=ib(t,s,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...s}=n;i={...i,...s,...e}}return i};function r(r){let{props:a}=t,o=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<sH;t++){let s=eD[t],n=e.props[s];(eR(n)||!1===n)&&(i[s]=n)}return i}(t.parent)||{},l=[],u=new Set,h={},d=1/0;for(let e=0;e<s_;e++){var c,p;let m=sX[e],f=i[m],g=void 0!==a[m]?a[m]:o[m],v=eR(g),y=m===r?f.isActive:null;!1===y&&(d=e);let x=g===o[m]&&g!==a[m]&&v;if(x&&s&&t.manuallyAnimateOnMount&&(x=!1),f.protectedKeys={...h},!f.isActive&&null===y||!g&&!f.prevProp||eV(g)||"boolean"==typeof g)continue;let w=(c=f.prevProp,"string"==typeof(p=g)?p!==c:!!Array.isArray(p)&&!sQ(p,c)),b=w||m===r&&f.isActive&&!x&&v||e>d&&v,A=!1,j=Array.isArray(g)?g:[g],T=j.reduce(n(m),{});!1===y&&(T={});let{prevResolvedValues:P={}}=f,S={...P,...T},k=e=>{b=!0,u.has(e)&&(A=!0,u.delete(e)),f.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in S){let e=T[t],i=P[t];if(h.hasOwnProperty(t))continue;let s=!1;(ij(e)&&ij(i)?sQ(e,i):e===i)?void 0!==e&&u.has(t)?k(t):f.protectedKeys[t]=!0:null!=e?k(t):u.add(t)}f.prevProp=g,f.prevResolvedValues=T,f.isActive&&(h={...h,...T}),s&&t.blockInitialAnimation&&(b=!1);let C=x&&w,E=!C||A;b&&E&&l.push(...j.map(e=>{let i={type:m};if("string"==typeof e&&s&&!C&&t.manuallyAnimateOnMount&&t.parent){let{parent:s}=t,n=ib(s,e);if(s.enteringChildren&&n){let{delayChildren:e}=n.transition||{};i.delay=sq(s.enteringChildren,t,e)}}return{animation:e,options:i}}))}if(u.size){let e={};if("boolean"!=typeof a.initial){let i=ib(t,Array.isArray(a.initial)?a.initial[0]:a.initial);i&&i.transition&&(e.transition=i.transition)}u.forEach(i=>{let s=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=s??null}),l.push({animation:e})}let m=!!l.length;return s&&(!1===a.initial||a.initial===a.animate)&&!t.manuallyAnimateOnMount&&(m=!1),s=!1,m?e(l):Promise.resolve()}return{animateChanges:r,setActive:function(e,s){if(i[e].isActive===s)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,s)),i[e].isActive=s;let n=r(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=sK(),s=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();eV(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let sJ=0;class s0 extends sG{constructor(){super(...arguments),this.id=sJ++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let s1={x:!1,y:!1};function s2(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}let s3=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function s5(t){return{point:{x:t.pageX,y:t.pageY}}}let s4=t=>e=>s3(e)&&t(e,s5(e));function s6(t,e,i,s){return s2(t,e,s4(i),s)}function s9(t){return t.max-t.min}function s8(t,e,i,s=.5){t.origin=s,t.originPoint=q(e.min,e.max,t.origin),t.scale=s9(i)/s9(e),t.translate=q(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function s7(t,e,i,s){s8(t.x,e.x,i.x,s?s.originX:void 0),s8(t.y,e.y,i.y,s?s.originY:void 0)}function nt(t,e,i){t.min=i.min+e.min,t.max=t.min+s9(e)}function ne(t,e,i){t.min=e.min-i.min,t.max=t.min+s9(e)}function ni(t,e,i){ne(t.x,e.x,i.x),ne(t.y,e.y,i.y)}function ns(t){return[t("x"),t("y")]}let nn=({current:t})=>t?t.ownerDocument.defaultView:null,nr=(t,e)=>Math.abs(t-e);class na{constructor(t,e,{transformPagePoint:i,contextWindow:s=window,dragSnapToOrigin:n=!1,distanceThreshold:r=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=nu(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(nr(t.x,e.x)**2+nr(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;let{point:s}=t,{timestamp:n}=tV;this.history.push({...s,timestamp:n});let{onStart:r,onMove:a}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=no(e,this.transformPagePoint),tE.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=nu("pointercancel"===t.type?this.lastMoveEventInfo:no(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),s&&s(t,r)},!s3(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=r,this.contextWindow=s||window;let a=no(s5(t),this.transformPagePoint),{point:o}=a,{timestamp:l}=tV;this.history=[{...o,timestamp:l}];let{onSessionStart:u}=e;u&&u(t,nu(a,this.history)),this.removeListeners=ik(s6(this.contextWindow,"pointermove",this.handlePointerMove),s6(this.contextWindow,"pointerup",this.handlePointerUp),s6(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),tM(this.updatePoint)}}function no(t,e){return e?{point:e(t.point)}:t}function nl(t,e){return{x:t.x-e.x,y:t.y-e.y}}function nu({point:t},e){return{point:t,delta:nl(t,nh(e)),offset:nl(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,n=nh(t);for(;i>=0&&(s=t[i],!(n.timestamp-s.timestamp>iC(.1)));)i--;if(!s)return{x:0,y:0};let r=iE(n.timestamp-s.timestamp);if(0===r)return{x:0,y:0};let a={x:(n.x-s.x)/r,y:(n.y-s.y)/r};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function nh(t){return t[t.length-1]}function nd(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function nc(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function np(t,e,i){return{min:nm(t,e),max:nm(t,i)}}function nm(t,e){return"number"==typeof t?t:t[e]||0}let nf=new WeakMap;class ng{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=eS(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:i}={}){let{presenceContext:s}=this.visualElement;if(s&&!1===s.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new na(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(s5(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:n}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(s1[t])return null;else return s1[t]=!0,()=>{s1[t]=!1};return s1.x||s1.y?null:(s1.x=s1.y=!0,()=>{s1.x=s1.y=!1})}(i),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ns(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tl.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=s9(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&tE.postRender(()=>n(t,e)),iT(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:n,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(a),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,a),this.updateAxis("y",e.point,a),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>ns(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,distanceThreshold:i,contextWindow:nn(this.visualElement)})}stop(t,e){let i=t||this.latestPointerEvent,s=e||this.latestPanInfo,n=this.isDragging;if(this.cancel(),!n||!s||!i)return;let{velocity:r}=s;this.startAnimation(r);let{onDragEnd:a}=this.getProps();a&&tE.postRender(()=>a(i,s))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!nv(t,s,this.currentDirection))return;let n=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?q(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?q(i,t,s.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),n.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;t&&ig(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:s,right:n}){return{x:nd(t.x,i,n),y:nd(t.y,e,s)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:np(t,"left","right"),y:np(t,"top","bottom")}}(e),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&ns(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!ig(e))return!1;let s=e.current;tv(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let r=function(t,e,i){let s=tt(t,i),{scroll:n}=e;return n&&(G(s.x,n.offset.x),G(s.y,n.offset.y)),s}(s,n.root,this.visualElement.getTransformPagePoint()),a=(t=n.layout.layoutBox,{x:nc(t.x,r.x),y:nc(t.y,r.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(a));this.hasMutatedConstraints=!!t,t&&(a=W(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:n,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(ns(a=>{if(!nv(a,e,this.currentDirection))return;let l=o&&o[a]||{};r&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[a]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return iT(this.visualElement,t),i.start(s$(t,i,0,e,this.visualElement,!1))}stopAnimation(){ns(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){ns(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){ns(e=>{let{drag:i}=this.getProps();if(!nv(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,n=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:r}=s.layout.layoutBox[e];n.set(t[e]-q(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!ig(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};ns(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=s9(t),n=s9(e);return n>s?i=sd(e.min,e.max-s,t.min):s>n&&(i=sd(t.min,t.max-n,e.min)),ti(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),ns(e=>{if(!nv(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:r}=this.constraints[e];i.set(q(n,r,s[e]))})}addListeners(){if(!this.visualElement.current)return;nf.set(this.visualElement,this);let t=s6(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();ig(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),tE.read(e);let n=s2(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(ns(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),s(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:n=!1,dragElastic:r=.35,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:n,dragElastic:r,dragMomentum:a}}}function nv(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class ny extends sG{constructor(t){super(t),this.removeGroupControls=tT,this.removeListeners=tT,this.controls=new ng(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||tT}unmount(){this.removeGroupControls(),this.removeListeners()}}let nx=t=>(e,i)=>{t&&tE.postRender(()=>t(e,i))};class nw extends sG{constructor(){super(...arguments),this.removePointerDownListener=tT}onPointerDown(t){this.session=new na(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nn(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:nx(t),onStart:nx(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&tE.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=s6(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let nb={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nA(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let nj={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tu.test(t))return t;else t=parseFloat(t);let i=nA(t,e.target.x),s=nA(t,e.target.y);return`${i}% ${s}%`}},nT=!1;class nP extends r.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:n}=t;for(let t in nk)eX[t]=nk[t],B(t)&&(eX[t].isCSSVariable=!0);n&&(e.group&&e.group.add(n),i&&i.register&&s&&i.register(n),nT&&n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),nb.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:n}=this.props,{projection:r}=i;return r&&(r.isPresent=n,nT=!0,s||t.layoutDependency!==e||void 0===e||t.isPresent!==n?r.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?r.promote():r.relegate()||tE.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),ew.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;nT=!0,s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nS(t){let[e,i]=function(t=!0){let e=(0,r.useContext)(iu);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:s,register:n}=e,a=(0,r.useId)();(0,r.useEffect)(()=>{if(t)return n(a)},[t]);let o=(0,r.useCallback)(()=>t&&s&&s(a),[a,s,t]);return!i&&s?[!1,o]:[!0]}(),s=(0,r.useContext)(e9);return(0,n.jsx)(nP,{...t,layoutGroup:s,switchLayoutGroup:(0,r.useContext)(iy),isPresent:e,safeToRemove:i})}let nk={borderRadius:{...nj,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:nj,borderTopRightRadius:nj,borderBottomLeftRadius:nj,borderBottomRightRadius:nj,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=t6.parse(t);if(s.length>5)return t;let n=t6.createTransformer(t),r=+("number"!=typeof s[0]),a=i.x.scale*e.x,o=i.y.scale*e.y;s[0+r]/=a,s[1+r]/=o;let l=q(a,o,.5);return"number"==typeof s[2+r]&&(s[2+r]/=l),"number"==typeof s[3+r]&&(s[3+r]/=l),n(s)}}};function nC(t){return"object"==typeof t&&null!==t}function nE(t){return nC(t)&&"ownerSVGElement"in t}let nM=(t,e)=>t.depth-e.depth;class nV{constructor(){this.children=[],this.isDirty=!1}add(t){ed(this.children,t),this.isDirty=!0}remove(t){ec(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nM),this.isDirty=!1,this.children.forEach(t)}}let nR=["TopLeft","TopRight","BottomLeft","BottomRight"],nN=nR.length,nD=t=>"string"==typeof t?parseFloat(t):t,nI=t=>"number"==typeof t||tu.test(t);function nF(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nL=nO(0,.5,sr),nB=nO(.5,.95,tT);function nO(t,e,i){return s=>s<t?0:s>e?1:i(sd(t,e,s))}function nU(t,e){t.min=e.min,t.max=e.max}function n$(t,e){nU(t.x,e.x),nU(t.y,e.y)}function nW(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nq(t,e,i,s,n){return t-=e,t=s+1/i*(t-s),void 0!==n&&(t=s+1/n*(t-s)),t}function nz(t,e,[i,s,n],r,a){!function(t,e=0,i=1,s=.5,n,r=t,a=t){if(tl.test(e)&&(e=parseFloat(e),e=q(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let o=q(r.min,r.max,s);t===r&&(o-=e),t.min=nq(t.min,e,i,o,n),t.max=nq(t.max,e,i,o,n)}(t,e[i],e[s],e[n],e.scale,r,a)}let nQ=["x","scaleX","originX"],nH=["y","scaleY","originY"];function nX(t,e,i,s){nz(t.x,e,nQ,i?i.x:void 0,s?s.x:void 0),nz(t.y,e,nH,i?i.y:void 0,s?s.y:void 0)}function n_(t){return 0===t.translate&&1===t.scale}function nY(t){return n_(t.x)&&n_(t.y)}function nK(t,e){return t.min===e.min&&t.max===e.max}function nG(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nZ(t,e){return nG(t.x,e.x)&&nG(t.y,e.y)}function nJ(t){return s9(t.x)/s9(t.y)}function n0(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class n1{constructor(){this.members=[]}add(t){ed(this.members,t),t.scheduleRender()}remove(t){if(ec(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let n2={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},n3=["","X","Y","Z"],n5=0;function n4(t,e,i,s){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),s&&(s[t]=0))}function n6({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:n}){return class{constructor(t={},i=e?.()){this.id=n5++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,tk.value&&(n2.nodes=n2.calculatedTargetDeltas=n2.calculatedProjections=0),this.nodes.forEach(n7),this.nodes.forEach(ra),this.nodes.forEach(ro),this.nodes.forEach(rt),tk.addProjectionMetrics&&tk.addProjectionMetrics(n2)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new nV)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new ep),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=nE(e)&&!(nE(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:s,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let i,s=0,n=()=>this.root.updateBlockedByResize=!1;tE.read(()=>{s=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==s&&(s=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=eh.now(),s=({timestamp:n})=>{let r=n-i;r>=250&&(tM(s),t(r-e))};return tE.setup(s,!0),()=>tM(s)}(n,250),nb.hasAnimatedSinceResize&&(nb.hasAnimatedSinceResize=!1,this.nodes.forEach(rr)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||n.getDefaultTransition()||rp,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=n.getProps(),l=!this.targetLayout||!nZ(this.targetLayout,s),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...iA(r,"layout"),onPlay:a,onComplete:o};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||rr(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),tM(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(rl),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=i.props[iv];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",tE,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(ri);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(rs);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(rn),this.nodes.forEach(n9),this.nodes.forEach(n8)):this.nodes.forEach(rs),this.clearAllSnapshots();let t=eh.now();tV.delta=ti(0,1e3/60,t-tV.timestamp),tV.timestamp=t,tV.isProcessing=!0,tR.update.process(tV),tR.preRender.process(tV),tR.render.process(tV),tV.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ew.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(re),this.sharedNodes.forEach(ru)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,tE.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){tE.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||s9(this.snapshot.measuredBox.x)||s9(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=eS(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nY(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,r=s!==this.prevTransformTemplateValue;t&&this.instance&&(e||H(this.latestValues)||r)&&(n(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),rg((e=s).x),rg(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return eS();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(ry))){let{scroll:t}=this.root;t&&(G(e.x,t.offset.x),G(e.y,t.offset.y))}return e}removeElementScroll(t){let e=eS();if(n$(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:n,options:r}=s;s!==this.root&&n&&r.layoutScroll&&(n.wasRoot&&n$(e,t),G(e.x,n.offset.x),G(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let i=eS();n$(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&J(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),H(s.latestValues)&&J(i,s.latestValues)}return H(this.latestValues)&&J(i,this.latestValues),i}removeTransform(t){let e=eS();n$(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!H(i.latestValues))continue;Q(i.latestValues)&&i.updateSnapshot();let s=eS();n$(s,i.measurePageBox()),nX(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return H(this.latestValues)&&nX(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==tV.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:n}=this.options;if(this.layout&&(s||n)){if(this.resolvedRelativeTargetAt=tV.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=eS(),this.relativeTargetOrigin=eS(),ni(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),n$(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=eS(),this.targetWithTransforms=eS()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var r,a,o;this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,o=this.relativeParent.target,nt(r.x,a.x,o.x),nt(r.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):n$(this.target,this.layout.layoutBox),K(this.target,this.targetDelta)):n$(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=eS(),this.relativeTargetOrigin=eS(),ni(this.relativeTargetOrigin,this.target,t.target),n$(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}tk.value&&n2.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Q(this.parent.latestValues)||X(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===tV.timestamp&&(i=!1),i)return;let{layout:s,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||n))return;n$(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,s=!1){let n,r,a=i.length;if(a){e.x=e.y=1;for(let o=0;o<a;o++){r=(n=i[o]).projectionDelta;let{visualElement:a}=n.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&J(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,K(t,r)),s&&H(n.latestValues)&&J(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=eS());let{target:o}=t;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nW(this.prevProjectionDelta.x,this.projectionDelta.x),nW(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),s7(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&n0(this.projectionDelta.x,this.prevProjectionDelta.x)&&n0(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),tk.value&&n2.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=eT(),this.projectionDelta=eT(),this.projectionDeltaWithTransform=eT()}setAnimationOrigin(t,e=!1){let i,s=this.snapshot,n=s?s.latestValues:{},r={...this.latestValues},a=eT();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let o=eS(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(rc));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(rh(a.x,t.x,s),rh(a.y,t.y,s),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,g;ni(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,g=s,rd(p.x,m.x,f.x,g),rd(p.y,m.y,f.y,g),i&&(u=this.relativeTarget,c=i,nK(u.x,c.x)&&nK(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=eS()),n$(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,s,n,r){n?(t.opacity=q(0,i.opacity??1,nL(s)),t.opacityExit=q(e.opacity??1,0,nB(s))):r&&(t.opacity=q(e.opacity??1,i.opacity??1,s));for(let n=0;n<nN;n++){let r=`border${nR[n]}Radius`,a=nF(e,r),o=nF(i,r);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||nI(a)===nI(o)?(t[r]=Math.max(q(nD(a),nD(o),s),0),(tl.test(o)||tl.test(a))&&(t[r]+="%")):t[r]=o)}(e.rotate||i.rotate)&&(t.rotate=q(e.rotate||0,i.rotate||0,s))}(r,n,this.latestValues,s,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(tM(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=tE.update(()=>{nb.hasAnimatedSinceResize=!0,iM.layout++,this.motionValue||(this.motionValue=ev(0)),this.currentAnimation=function(t,e,i){let s=el(t)?t:ev(t);return s.start(s$("",s,e,i)),s.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{iM.layout--},onComplete:()=>{iM.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:n}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&rv(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||eS();let e=s9(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=s9(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}n$(e,i),J(e,n),s7(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new n1),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&n4("z",t,s,this.animationValues);for(let e=0;e<n3.length;e++)n4(`rotate${n3[e]}`,t,s,this.animationValues),n4(`skew${n3[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=ih(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=ih(e?.pointerEvents)||""),this.hasProjected&&!H(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let n=s.animationValues||s.latestValues;this.applyTransformsToTarget();let r=function(t,e,i){let s="",n=t.x.translate/e.x,r=t.y.translate/e.y,a=i?.z||0;if((n||r||a)&&(s=`translate3d(${n}px, ${r}px, ${a}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:r,skewX:a,skewY:o}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),n&&(s+=`rotateX(${n}deg) `),r&&(s+=`rotateY(${r}deg) `),a&&(s+=`skewX(${a}deg) `),o&&(s+=`skewY(${o}deg) `)}let o=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==o||1!==l)&&(s+=`scale(${o}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,n);i&&(r=i(n,r)),t.transform=r;let{x:a,y:o}=this.projectionDelta;for(let e in t.transformOrigin=`${100*a.origin}% ${100*o.origin}% 0`,s.animationValues?t.opacity=s===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:t.opacity=s===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,eX){if(void 0===n[e])continue;let{correct:i,applyTo:a,isCSSVariable:o}=eX[e],l="none"===r?n[e]:i(n[e],s);if(a){let e=a.length;for(let i=0;i<e;i++)t[a[i]]=l}else o?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=s===this?ih(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(ri),this.root.sharedNodes.clear()}}}function n9(t){t.updateLayout()}function n8(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:s}=t.layout,{animationType:n}=t.options,r=e.source!==t.layout.source;"size"===n?ns(t=>{let s=r?e.measuredBox[t]:e.layoutBox[t],n=s9(s);s.min=i[t].min,s.max=s.min+n}):rv(n,e.layoutBox,i)&&ns(s=>{let n=r?e.measuredBox[s]:e.layoutBox[s],a=s9(i[s]);n.max=n.min+a,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+a)});let a=eT();s7(a,i,e.layoutBox);let o=eT();r?s7(o,t.applyTransform(s,!0),e.measuredBox):s7(o,i,e.layoutBox);let l=!nY(a),u=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:n,layout:r}=s;if(n&&r){let a=eS();ni(a,e.layoutBox,n.layoutBox);let o=eS();ni(o,i,r.layoutBox),nZ(a,o)||(u=!0),s.options.layoutRoot&&(t.relativeTarget=o,t.relativeTargetOrigin=a,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function n7(t){tk.value&&n2.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function rt(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function re(t){t.clearSnapshot()}function ri(t){t.clearMeasurements()}function rs(t){t.isLayoutDirty=!1}function rn(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function rr(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ra(t){t.resolveTargetDelta()}function ro(t){t.calcProjection()}function rl(t){t.resetSkewAndRotation()}function ru(t){t.removeLeadSnapshot()}function rh(t,e,i){t.translate=q(e.translate,0,i),t.scale=q(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function rd(t,e,i,s){t.min=q(e.min,i.min,s),t.max=q(e.max,i.max,s)}function rc(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let rp={duration:.45,ease:[.4,0,.1,1]},rm=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),rf=rm("applewebkit/")&&!rm("chrome/")?Math.round:tT;function rg(t){t.min=rf(t.min),t.max=rf(t.max)}function rv(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nJ(e)-nJ(i)))}function ry(t){return t!==t.root&&t.scroll?.wasRoot}let rx=n6({attachResizeListener:(t,e)=>s2(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rw={current:void 0},rb=n6({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rw.current){let t=new rx({});t.mount(window),t.setOptions({layoutScroll:!0}),rw.current=t}return rw.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function rA(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function rj(t){return!("touch"===t.pointerType||s1.x||s1.y)}function rT(t,e,i){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=s["onHover"+i];n&&tE.postRender(()=>n(e,s5(e)))}class rP extends sG{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=rA(t,i),a=t=>{if(!rj(t))return;let{target:i}=t,s=e(i,t);if("function"!=typeof s||!i)return;let r=t=>{rj(t)&&(s(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,n)};return s.forEach(t=>{t.addEventListener("pointerenter",a,n)}),r}(t,(t,e)=>(rT(this.node,e,"Start"),t=>rT(this.node,t,"End"))))}unmount(){}}class rS extends sG{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ik(s2(this.node.current,"focus",()=>this.onFocus()),s2(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rk=(t,e)=>!!e&&(t===e||rk(t,e.parentElement)),rC=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rE=new WeakSet;function rM(t){return e=>{"Enter"===e.key&&t(e)}}function rV(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let rR=(t,e)=>{let i=t.currentTarget;if(!i)return;let s=rM(()=>{if(rE.has(i))return;rV(i,"down");let t=rM(()=>{rV(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>rV(i,"cancel"),e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)};function rN(t){return s3(t)&&!(s1.x||s1.y)}function rD(t,e,i){let{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=s["onTap"+("End"===i?"":i)];n&&tE.postRender(()=>n(e,s5(e)))}class rI extends sG{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=rA(t,i),a=t=>{let s=t.currentTarget;if(!rN(t))return;rE.add(s);let r=e(s,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),rE.has(s)&&rE.delete(s),rN(t)&&"function"==typeof r&&r(t,{success:e})},o=t=>{a(t,s===window||s===document||i.useGlobalTarget||rk(s,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,n),window.addEventListener("pointercancel",l,n)};return s.forEach(t=>{(i.useGlobalTarget?window:t).addEventListener("pointerdown",a,n),nC(t)&&"offsetHeight"in t&&(t.addEventListener("focus",t=>rR(t,n)),rC.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}(t,(t,e)=>(rD(this.node,e,"Start"),(t,{success:e})=>rD(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rF=new WeakMap,rL=new WeakMap,rB=t=>{let e=rF.get(t.target);e&&e(t)},rO=t=>{t.forEach(rB)},rU={some:0,all:1};class r$ extends sG{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:n}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:rU[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;rL.has(i)||rL.set(i,{});let s=rL.get(i),n=JSON.stringify(e);return s[n]||(s[n]=new IntersectionObserver(rO,{root:t,...e})),s[n]}(e);return rF.set(t,i),s.observe(t),()=>{rF.delete(t),s.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),r=e?i:s;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rW=function(t,e){if("undefined"==typeof Proxy)return iw;let i=new Map,s=(i,s)=>iw(i,s,t,e);return new Proxy((t,e)=>s(t,e),{get:(n,r)=>"create"===r?s:(i.has(r)||i.set(r,iw(r,void 0,t,e)),i.get(r))})}({animation:{Feature:sZ},exit:{Feature:s0},inView:{Feature:r$},tap:{Feature:rI},focus:{Feature:rS},hover:{Feature:rP},pan:{Feature:nw},drag:{Feature:ny,ProjectionNode:rb,MeasureLayout:nS},layout:{ProjectionNode:rb,MeasureLayout:nS}},(t,e)=>e6(t)?new e5(e):new eK(e,{allowProjection:t!==r.Fragment})),rq="https://api.d-id.com/talks",rz=({text:t,videoUrl:e,onVideoReady:i,onVideoEnd:s,className:a="",preloadingService:o,showPreloadingStatus:l=!1,priority:u=0})=>{let[h,d]=(0,r.useState)(e||null),[c,p]=(0,r.useState)(!1),[m,f]=(0,r.useState)(null),[g,v]=(0,r.useState)(null),y=(0,r.useRef)(null),x=async t=>{if(t.trim()){p(!0),d(null),f(null);try{let e=null;if(o)try{e=await o.getVideoUrl(t,u)}catch(t){console.warn("Failed to get video from preloading service, falling back to direct API:",t)}if(!e){let i={script:{type:"text",input:t.trim()}},s=await fetch(rq,{method:"POST",headers:{Authorization:`Basic ${btoa("******************************:iA53PWR1twRC9ciLUfrS_")}`,"Content-Type":"application/json"},body:JSON.stringify(i)});if(!s.ok)throw Error(`Failed to create talk: ${s.statusText}`);let n=(await s.json()).id,r=0;for(;!e&&r<30;){let t=await fetch(`${rq}/${n}`,{headers:{Authorization:`Basic ${btoa("******************************:iA53PWR1twRC9ciLUfrS_")}`,"Content-Type":"application/json"}}),i=await t.json();if("done"===i.status&&i.result_url)e=i.result_url;else if("error"===i.status)throw Error("Video generation failed");else await new Promise(t=>setTimeout(t,3e3)),r++}if(!e)throw Error("Video generation timed out")}d(e),i?.()}catch(t){console.error("D-ID API Error:",t),f(t.message||"Failed to generate video")}finally{p(!1)}}};return(0,r.useEffect)(()=>{if(o&&l){let t=setInterval(()=>{v(o.getQueueStatus())},1e3);return()=>clearInterval(t)}},[o,l]),(0,r.useEffect)(()=>{e&&(d(e),p(!1),f(null))},[e]),(0,r.useEffect)(()=>{t&&t.trim()&&!e&&x(t)},[t,e]),(0,r.useEffect)(()=>{let t=y.current;if(t){let e=()=>{s?.()};return t.addEventListener("ended",e),()=>{t.removeEventListener("ended",e)}}},[h,s]),(0,n.jsxs)("div",{className:`relative w-full h-full ${a}`,children:[(0,n.jsx)("div",{className:"w-full h-full relative",children:!h||c||m?(0,n.jsxs)(rW.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.3},className:"w-full h-full bg-gradient-to-br from-blue-400 via-indigo-500 to-purple-600 flex flex-col items-center justify-center relative rounded-lg",children:[(0,n.jsx)(j,{className:"w-16 h-16 sm:w-24 sm:h-24 lg:w-32 lg:h-32 text-white/90 mb-4 lg:mb-8"}),(0,n.jsxs)("div",{className:"text-center text-white px-4",children:[(0,n.jsx)("h3",{className:"text-lg sm:text-xl lg:text-2xl font-bold mb-1 lg:mb-2",children:"AI Interviewer"}),(0,n.jsxs)("div",{className:"flex items-center justify-center space-x-2 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 lg:px-4 lg:py-2",children:[(0,n.jsx)("div",{className:"w-2 h-2 lg:w-3 lg:h-3 bg-green-400 rounded-full animate-pulse"}),(0,n.jsx)("span",{className:"text-white font-medium text-sm lg:text-base",children:"Ready"})]})]}),(0,n.jsx)("div",{className:"absolute top-4 left-4 lg:top-8 lg:left-8 w-8 h-8 lg:w-16 lg:h-16 bg-white/10 rounded-full blur-xl"}),(0,n.jsx)("div",{className:"absolute bottom-6 right-6 lg:bottom-12 lg:right-12 w-12 h-12 lg:w-24 lg:h-24 bg-white/5 rounded-full blur-2xl"}),(0,n.jsx)("div",{className:"absolute top-1/3 right-4 lg:right-8 w-4 h-4 lg:w-8 lg:h-8 bg-white/20 rounded-full blur-sm"})]},"placeholder"):(0,n.jsxs)(rW.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.3},className:"w-full h-full relative",children:[(0,n.jsx)("video",{ref:y,src:h,autoPlay:!0,className:"w-full h-full object-cover rounded-lg",onLoadedData:()=>i?.()},h),(0,n.jsxs)(rW.div,{initial:{scale:0},animate:{scale:1},className:"absolute top-4 right-4 flex items-center space-x-2 bg-black/20 backdrop-blur-sm rounded-full px-3 py-2",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),(0,n.jsx)("span",{className:"text-white text-sm font-medium",children:"Speaking"})]})]},h)}),m&&(0,n.jsx)(rW.div,{initial:{opacity:0},animate:{opacity:1},className:"absolute inset-0 bg-red-50 flex flex-col items-center justify-center rounded-lg border-2 border-red-200",children:(0,n.jsxs)("div",{className:"text-center text-red-600 px-4",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Error"}),(0,n.jsx)("p",{className:"text-sm",children:m})]})})]})},rQ="https://api.d-id.com/talks";class rH extends Error{constructor(t,e){super(t),this.statusCode=e,this.name="DIDApiError"}}class rX{constructor(t){this.cache={},this.preloadQueue=[],this.activeRequests=0,this.abortController=null,this.config={...t,cacheExpiration:t.cacheExpiration||36e5,maxConcurrentRequests:t.maxConcurrentRequests||3,retryAttempts:t.retryAttempts||2,sourceUrl:t.sourceUrl??""},this.loadCacheFromStorage(),this.cleanExpiredCache()}generateCacheKey(t){let e=0;for(let i=0;i<t.length;i++)e=(e<<5)-e+t.charCodeAt(i),e&=e;return`did_video_${Math.abs(e)}`}loadCacheFromStorage(){}saveCacheToStorage(){}cleanExpiredCache(){let t=Date.now(),e=!1;for(let i in this.cache)this.cache[i].expiresAt<t&&(delete this.cache[i],e=!0);e&&this.saveCacheToStorage()}async generateVideo(t,e=0){let i={script:{type:"text",input:t.trim()},...this.config.sourceUrl&&{source_url:this.config.sourceUrl}};try{let t=await fetch(rQ,{method:"POST",headers:{Authorization:`Basic ${btoa(this.config.apiKey)}`,"Content-Type":"application/json"},body:JSON.stringify(i),signal:this.abortController?.signal});if(!t.ok)throw new rH(`Failed to create talk: ${t.statusText}`,t.status);let e=(await t.json()).id,s=0;for(;s<30;){if(this.abortController?.signal.aborted)throw new rH("Request aborted");let t=await fetch(`${rQ}/${e}`,{headers:{Authorization:`Basic ${btoa(this.config.apiKey)}`,"Content-Type":"application/json"},signal:this.abortController?.signal}),i=await t.json();if("done"===i.status&&i.result_url)return i.result_url;if("error"===i.status)throw new rH("Video generation failed");await new Promise(t=>setTimeout(t,3e3)),s++}throw new rH("Video generation timed out")}catch(i){if(e<this.config.retryAttempts&&!(i instanceof rH&&401===i.statusCode))return console.warn(`Retrying video generation (attempt ${e+1}):`,i),await new Promise(t=>setTimeout(t,1e3*(e+1))),this.generateVideo(t,e+1);throw console.error("D-ID API Error:",i),i instanceof rH?i:new rH(i instanceof Error?i.message:"Unknown error generating video")}}async processQueue(){for(;this.preloadQueue.length>0&&this.activeRequests<this.config.maxConcurrentRequests;){let t=this.preloadQueue.findIndex(t=>"pending"===t.status);if(-1===t)break;let e=this.preloadQueue[t];e.status="loading",this.activeRequests++;try{let i=await this.generateVideo(e.text),s=this.generateCacheKey(e.text),n=Date.now();this.cache[s]={url:i,timestamp:n,expiresAt:n+this.config.cacheExpiration},this.saveCacheToStorage(),e.status="completed",this.preloadQueue.splice(t,1)}catch(i){e.status="failed",e.error=i instanceof Error?i.message:"Unknown error",this.preloadQueue.splice(t,1)}finally{this.activeRequests--}}}async getVideoUrl(t,e=0){if(!t.trim())throw new rH("Question text is required");let i=this.generateCacheKey(t),s=this.cache[i];if(s&&s.expiresAt>Date.now())return s.url;let n=this.preloadQueue.find(e=>e.text===t);if(n){if(n.promise)return n.promise;e>n.priority&&(n.priority=e,this.preloadQueue.sort((t,e)=>e.priority-t.priority))}else{let s={id:`task_${Date.now()}_${Math.random()}`,text:t,priority:e,status:"pending"};s.promise=new Promise((t,e)=>{let n=()=>{let r=this.preloadQueue.find(t=>t.id===s.id);if(r)"failed"===r.status?e(Error(r.error||"Video generation failed")):setTimeout(n,500);else{let s=this.cache[i];s?t(s.url):e(Error("Task completed but no cached result found"))}};setTimeout(n,100)}),this.preloadQueue.push(s),this.preloadQueue.sort((t,e)=>e.priority-t.priority)}return this.processQueue(),n?.promise||this.preloadQueue.find(e=>e.text===t)?.promise||Promise.reject(Error("Failed to create task"))}preloadVideos(t,e=0){t.forEach((t,i)=>{this.getVideoUrl(t,e-i).catch(e=>{console.warn(`Failed to preload video for: "${t.substring(0,50)}..."`,e)})})}getQueueStatus(){let t=this.preloadQueue.filter(t=>"pending"===t.status).length,e=this.preloadQueue.filter(t=>"loading"===t.status).length;return{pending:t,loading:e,completed:this.preloadQueue.filter(t=>"completed"===t.status).length,failed:this.preloadQueue.filter(t=>"failed"===t.status).length,total:this.preloadQueue.length}}clearCache(){this.cache={},this.saveCacheToStorage()}abort(){this.abortController&&this.abortController.abort(),this.abortController=new AbortController,this.preloadQueue=[],this.activeRequests=0}destroy(){this.abort(),this.clearCache()}}let r_=t=>{let{questions:e,candidateName:i="Candidate",jobTitle:s="Position",apiKey:n,sourceUrl:a,autoStart:o=!1}=t,[l,u]=(0,r.useState)({isPreparingInterview:!1,progress:0,currentStep:"",totalQuestions:e.length,loadedQuestions:0,failedQuestions:0,estimatedTimeRemaining:0,error:null}),[h,d]=(0,r.useState)(0),[c,p]=(0,r.useState)(""),[m,f]=(0,r.useState)(!1),g=(0,r.useRef)(null),v=(0,r.useRef)(null),y=(0,r.useRef)(0),x=e.map((t,e)=>0===e?t.replace(/\{candidateName\}/g,i).replace(/\{jobTitle\}/g,s):t),w=(0,r.useCallback)(t=>{u(e=>({...e,...t}))},[]),b=(0,r.useCallback)((t,e,i)=>0===t?0:Math.ceil((Date.now()-i)/t*(e-t)/1e3),[]),A=(0,r.useCallback)(async()=>{if(!n)return void w({error:"API key is required for video generation",isPreparingInterview:!1});w({isPreparingInterview:!0,progress:0,currentStep:"Initializing interview preparation...",error:null}),y.current=Date.now();try{g.current=new rX({apiKey:n,sourceUrl:a,cacheExpiration:36e5,maxConcurrentRequests:3,retryAttempts:2}),w({currentStep:"Starting background video generation..."}),g.current.preloadVideos(x,100),v.current=setInterval(()=>{if(g.current){let t=g.current.getQueueStatus(),e=t.total>0?t.completed/t.total*100:0,i=b(t.completed,t.total,y.current);w({progress:e,loadedQuestions:t.completed,failedQuestions:t.failed,estimatedTimeRemaining:i,currentStep:t.loading>0?`Generating video ${t.completed+1} of ${t.total}...`:t.completed===t.total?"All questions ready!":"Preparing questions..."}),t.completed+t.failed>=t.total&&(v.current&&(clearInterval(v.current),v.current=null),j(0),w({isPreparingInterview:!1,currentStep:"Interview ready to start!"}),f(!0))}},500)}catch(t){console.error("Failed to start interview preparation:",t),w({error:t instanceof Error?t.message:"Failed to prepare interview",isPreparingInterview:!1})}},[n,a,x,w,b]),j=(0,r.useCallback)(async t=>{if(!g.current||t>=x.length)return;let e=x[t];try{let i=await g.current.getVideoUrl(e,1e3-t);p(i),d(t)}catch(e){console.error(`Failed to load question ${t}:`,e),p("")}},[x]),T=(0,r.useCallback)(async()=>{let t=h+1;return t<x.length&&(await j(t),!0)},[h,x.length,j]),P=(0,r.useCallback)(()=>x[h]||"",[x,h]),S=(0,r.useCallback)(()=>x.length>0?(h+1)/x.length*100:0,[x.length,h]);return(0,r.useEffect)(()=>{!o||l.isPreparingInterview||m||A()},[o,l.isPreparingInterview,m,A]),(0,r.useEffect)(()=>()=>{v.current&&clearInterval(v.current),g.current&&g.current.destroy()},[]),{...l,isInterviewReady:m,currentQuestionIndex:h,currentVideoUrl:c,currentQuestionText:P(),interviewProgress:S(),startPreparation:A,nextQuestion:T,loadQuestion:j,preloadingService:g.current,personalizedQuestions:x,isLastQuestion:h>=x.length-1}},rY=({isVisible:t})=>t?(0,n.jsx)(rW.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50",children:(0,n.jsx)(rW.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-white/95 backdrop-blur-sm rounded-2xl p-8 text-center shadow-2xl mx-4 max-w-md w-full",children:(0,n.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:"Preparing Interview"})})}):null,rK=({onNext:t,candidateName:e="Jonathan",jobTitle:i="Insurance Agent"})=>{let[s,o]=(0,r.useState)(!1),[u,h]=(0,r.useState)(!1),d=["Hello {candidateName}, welcome to your interview for the {jobTitle} position. Let's start with our first question: Tell us about yourself and your background."],c=r_({questions:d,candidateName:e,jobTitle:i,apiKey:"******************************:iA53PWR1twRC9ciLUfrS_",autoStart:!1}),p=async()=>{o(!0),await c.startPreparation()},m=async()=>{c.isLastQuestion?t?.():(h(!1),await c.nextQuestion()||t?.())},v=c.isLastQuestion&&c.currentQuestionIndex>=d.length;return s?v?(0,n.jsxs)("div",{className:"h-screen",children:[(0,n.jsx)(f,{}),(0,n.jsx)(w,{children:(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center h-full",children:[(0,n.jsxs)("div",{className:"text-center mb-8",children:[(0,n.jsx)(A,{className:"w-16 h-16 text-green-500 mx-auto mb-4"}),(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Interview Completed!"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Thank you for completing the interview. Your responses have been recorded."})]}),(0,n.jsxs)(a.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>t?.(),children:["View Results",(0,n.jsx)(l,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})]})})]}):(0,n.jsxs)("div",{className:"h-screen",children:[(0,n.jsx)(f,{}),(0,n.jsxs)(w,{children:[(0,n.jsxs)("div",{className:"flex flex-col md:flex-row gap-10 justify-center items-center md:items-start",children:[(0,n.jsx)(g,{className:"h-[550px]",currentQuestion:c.currentQuestionIndex+1}),(0,n.jsx)("div",{className:"mt-6 md:mt-0",children:(0,n.jsx)(rz,{className:"w-[300px] h-[300px]",text:c.currentQuestionText,videoUrl:c.currentVideoUrl,onVideoReady:()=>{setTimeout(()=>{h(!0)},2e3)},onVideoEnd:()=>{u||h(!0)}})})]}),(0,n.jsx)(rY,{isVisible:c.isPreparingInterview,progress:c.progress,currentStep:c.currentStep,totalQuestions:c.totalQuestions,loadedQuestions:c.loadedQuestions,failedQuestions:c.failedQuestions,estimatedTimeRemaining:c.estimatedTimeRemaining}),(0,n.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:u?(0,n.jsxs)(a.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:m,children:[c.isLastQuestion?"Finish Interview":"Next Question",(0,n.jsx)(l,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]}):(0,n.jsx)("div",{className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center justify-center gap-2 bg-gray-200 text-gray-500",children:"Listen to the question"})})]})]}):(0,n.jsxs)("div",{className:"h-screen",children:[(0,n.jsx)(f,{}),(0,n.jsxs)(w,{children:[(0,n.jsxs)("div",{className:"flex flex-col md:flex-row gap-10 justify-center items-center md:items-start",children:[(0,n.jsx)(g,{className:"h-[550px]",currentQuestion:0}),(0,n.jsx)("div",{className:"mt-6 md:mt-0",children:(0,n.jsx)(rz,{className:"w-[300px] h-[300px]"})})]}),(0,n.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:(0,n.jsxs)(a.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:p,children:["Start Interview",(0,n.jsx)(l,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})}),(0,n.jsx)("div",{className:"flex justify-center mt-5 text-2xl font-semibold text-primary",children:"Ready to begin"})]})]})},rG=()=>(0,n.jsxs)("div",{className:"rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] shadow-sm h-[488px] overflow-y-auto scrollbar-hidden",children:[(0,n.jsx)("p",{className:"text-lg font-semibold text-black mb-5",children:"Video Transcript"}),(0,n.jsx)("p",{children:"Tell us about yourselves?"}),(0,n.jsx)("p",{className:"text-sm mt-4 leading-7 ",children:"Motivated and results-driven professional with a proven track record of success in dynamic work environments. Known for strong problem-solving skills, a collaborative mindset, and a dedication to continuous learning and improvement. Brings a blend of technical expertise, strategic thinking, and effective communication to contribute meaningfully to team and organizational goals. Eager to take on new challenges and deliver impactful outcomes in a fast-paced role."})]}),rZ=({onNext:t})=>(0,n.jsxs)("div",{className:"h-screen",children:[(0,n.jsx)(f,{}),(0,n.jsxs)(w,{children:[(0,n.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start",children:[(0,n.jsx)(g,{}),(0,n.jsx)(x,{className:"w-[265px]"}),(0,n.jsx)(rG,{})]}),(0,n.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:(0,n.jsxs)(a.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>t&&t(),children:["Finish Interview",(0,n.jsx)(l,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})})]})]}),rJ={src:"/_next/static/media/trophy.73528452.png",height:28,width:28,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAANlBMVEXNszH7qED//1HPmRGtnSfDoyrn1T1MaXHr1j+cqzrVpR7SoiDosSnluR7esSLrwyPptBvv0S75zPcvAAAAEnRSTlMR/hacHCkqADMIjM+nl9x4XaVFuRFRAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAPElEQVR4nBXFSRIAIQgAsUZBwN3/f3ZqcglJMSskhKpq/Pe9uwZWn8irRrtLZN0GkedkgLecM5vjzhi4fzkhAbtZdsbsAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},r0=()=>(0,n.jsxs)("div",{className:"flex  justify-between bg-white rounded-2xl shadow-md p-4 w-full max-w-xl mb-5",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsxs)("div",{className:"bg-[#F4F1FE] rounded-xl px-4 py-4 text-center w-30",children:[(0,n.jsx)("div",{className:"flex justify-center mb-2",children:(0,n.jsx)(v.default,{src:rJ,alt:"Trophy"})}),(0,n.jsx)("p",{className:"text-xl font-bold text-[#1E1E1E]",children:"55%"}),(0,n.jsx)("p",{className:"text-xs text-gray-600 mt-1",children:"Overall Score"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold text-sm sm:text-[6px] md:text-base lg:text-lg text-[#1E1E1E] mb-2",children:"AI Interviewer"}),(0,n.jsx)("p",{className:"text-sm text-gray-800 font-medium",children:"UI UX Designer"}),(0,n.jsx)("p",{className:"text-sm text-gray-800 font-medium",children:"18th June, 2025"})]})]}),(0,n.jsx)("div",{className:"top-0",children:(0,n.jsx)("span",{className:"bg-[#CCFFB1] text-[#1E1E1E] text-xs px-4 py-1 rounded-full",children:"Evaluated"})})]}),r1=({label:t,value:e,color:i="bg-orange-500"})=>(0,n.jsxs)("div",{className:"mb-2",children:[(0,n.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,n.jsx)("span",{className:"mb-1",children:t}),(0,n.jsxs)("span",{children:[e,"/100"]})]}),(0,n.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,n.jsx)("div",{className:`h-2.5 rounded-full ${i}`,style:{width:`${e}%`}})})]});var r2=function(t,e){return(r2=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)};function r3(t){var e,i,s,n,a,o,l,u,h=t.className,d=t.counterClockwise,c=t.dashRatio,p=t.pathRadius,m=t.strokeWidth,f=t.style;return(0,r.createElement)("path",{className:h,style:Object.assign({},f,(i=(e={pathRadius:p,dashRatio:c,counterClockwise:d}).counterClockwise,s=e.dashRatio,a=(1-s)*(n=2*Math.PI*e.pathRadius),{strokeDasharray:n+"px "+n+"px",strokeDashoffset:(i?-a:a)+"px"})),d:(l=(o={pathRadius:p,counterClockwise:d}).pathRadius,"\n      M 50,50\n      m 0,-"+l+"\n      a "+l+","+l+" "+(u=+!!o.counterClockwise)+" 1 1 0,"+2*l+"\n      a "+l+","+l+" "+u+" 1 1 0,-"+2*l+"\n    "),strokeWidth:m,fillOpacity:0})}var r5=function(t){function e(){this.constructor=i}function i(){return null!==t&&t.apply(this,arguments)||this}return r2(i,t),i.prototype=null===t?Object.create(t):(e.prototype=t.prototype,new e),i.prototype.getBackgroundPadding=function(){return this.props.background?this.props.backgroundPadding:0},i.prototype.getPathRadius=function(){return 50-this.props.strokeWidth/2-this.getBackgroundPadding()},i.prototype.getPathRatio=function(){var t=this.props,e=t.value,i=t.minValue,s=t.maxValue;return(Math.min(Math.max(e,i),s)-i)/(s-i)},i.prototype.render=function(){var t=this.props,e=t.circleRatio,i=t.className,s=t.classes,n=t.counterClockwise,a=t.styles,o=t.strokeWidth,l=t.text,u=this.getPathRadius(),h=this.getPathRatio();return(0,r.createElement)("svg",{className:s.root+" "+i,style:a.root,viewBox:"0 0 100 100","data-test-id":"CircularProgressbar"},this.props.background?(0,r.createElement)("circle",{className:s.background,style:a.background,cx:50,cy:50,r:50}):null,(0,r.createElement)(r3,{className:s.trail,counterClockwise:n,dashRatio:e,pathRadius:u,strokeWidth:o,style:a.trail}),(0,r.createElement)(r3,{className:s.path,counterClockwise:n,dashRatio:h*e,pathRadius:u,strokeWidth:o,style:a.path}),l?(0,r.createElement)("text",{className:s.text,style:a.text,x:50,y:50},l):null)},i.defaultProps={background:!1,backgroundPadding:0,circleRatio:1,classes:{root:"CircularProgressbar",trail:"CircularProgressbar-trail",path:"CircularProgressbar-path",text:"CircularProgressbar-text",background:"CircularProgressbar-background"},counterClockwise:!1,className:"",maxValue:100,minValue:0,strokeWidth:8,styles:{root:{},trail:{},path:{},text:{},background:{}},text:""},i}(r.Component);function r4(t){return Object.keys(t).forEach(function(e){null==t[e]&&delete t[e]}),t}i(9587);let r6=({label:t,percent:e,color:i,trailColor:s})=>(0,n.jsxs)("div",{className:"flex flex-col items-center space-y-1 mb-2",children:[(0,n.jsx)("p",{className:"text-sm font-semibold mb-3",children:t}),(0,n.jsx)("div",{className:"w-32 h-28",children:(0,n.jsx)(r5,{value:e,text:`${e}%`,strokeWidth:10,styles:function(t){var e=t.rotation,i=t.strokeLinecap,s=t.textColor,n=t.textSize,r=t.pathColor,a=t.pathTransition,o=t.pathTransitionDuration,l=t.trailColor,u=t.backgroundColor,h=null==e?void 0:"rotate("+e+"turn)",d=null==e?void 0:"center center";return{root:{},path:r4({stroke:r,strokeLinecap:i,transform:h,transformOrigin:d,transition:a,transitionDuration:null==o?void 0:o+"s"}),trail:r4({stroke:l,strokeLinecap:i,transform:h,transformOrigin:d}),text:r4({fill:s,fontSize:n}),background:r4({fill:u})}}({textSize:"12px",pathColor:i,textColor:"#5a5a5a",trailColor:s})})})]}),r9=()=>(0,n.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 border p-6 rounded-xl w-full max-w-6xl mx-auto",children:[(0,n.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,n.jsxs)("div",{className:"flex justify-between font-semibold mb-4",children:[(0,n.jsx)("span",{children:"Resume Score"}),(0,n.jsx)("span",{children:"65%"})]}),(0,n.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,n.jsx)(r1,{label:"Company Fit",value:66}),(0,n.jsx)(r1,{label:"Relevant Experience",value:66,color:"bg-purple-600"}),(0,n.jsx)(r1,{label:"Job Knowledge",value:66}),(0,n.jsx)(r1,{label:"Education",value:66}),(0,n.jsx)(r1,{label:"Hard Skills",value:66})]}),(0,n.jsxs)("div",{className:"mt-4 font-medium flex justify-between bg-gray-100 text-sm text-center border rounded-xl p-8",children:["Over All Score \xa0 ",(0,n.jsx)("span",{className:"text-black",children:"66/100"})]})]}),(0,n.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,n.jsx)("div",{className:"font-semibold mb-4",children:"Video Score"}),(0,n.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,n.jsx)(r1,{label:"Professionalism",value:64}),(0,n.jsx)(r1,{label:"Energy Level",value:56,color:"bg-purple-600"}),(0,n.jsx)(r1,{label:"Communication",value:58}),(0,n.jsx)(r1,{label:"Sociability",value:70})]})]}),(0,n.jsxs)("div",{className:"bg-white rounded-lg p-4 flex flex-col space-y-2   gap-5 shadow-sm",children:[(0,n.jsx)("p",{className:"font-semibold",children:"AI Rating"}),(0,n.jsx)(r6,{label:"AI Resume Rating",percent:75,color:"#A855F7",trailColor:"#EAE2FF"}),(0,n.jsx)(r6,{label:"AI Video Rating",percent:75,color:"#FF5B00",trailColor:"#FFEAE1"})]})]}),r8=()=>(0,n.jsxs)("div",{className:"h-screen",children:[(0,n.jsx)(r0,{}),(0,n.jsx)(w,{children:(0,n.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start",children:[(0,n.jsx)(g,{}),(0,n.jsx)(x,{className:"w-[265px]"}),(0,n.jsx)(rG,{})]})}),(0,n.jsx)(r9,{})]}),r7=()=>{let[t,e]=(0,r.useState)("instructions");return(0,n.jsx)("div",{children:(()=>{switch(t){case"instructions":default:return(0,n.jsx)(c,{onNext:()=>e("questions")});case"questions":return(0,n.jsx)(b,{onNext:()=>e("recording")});case"recording":return(0,n.jsx)(rK,{onNext:()=>e("finishInterview")});case"finishInterview":return(0,n.jsx)(rZ,{onNext:()=>e("analysis")});case"analysis":return(0,n.jsx)(r8,{})}})()})}},3873:t=>{"use strict";t.exports=require("path")},4934:(t,e,i)=>{"use strict";i.d(e,{$:()=>l});var s=i(687);i(3210);var n=i(1391),r=i(4224),a=i(6241);let o=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:t,variant:e,size:i,asChild:r=!1,...l}){let u=r?n.DX:"button";return(0,s.jsx)(u,{"data-slot":"button",className:(0,a.cn)(o({variant:e,size:i,className:t})),...l})}},5511:t=>{"use strict";t.exports=require("crypto")},6241:(t,e,i)=>{"use strict";i.d(e,{cn:()=>r});var s=i(9384),n=i(2348);function r(...t){return(0,n.QP)((0,s.$)(t))}},6723:(t,e,i)=>{Promise.resolve().then(i.bind(i,1079))},7089:(t,e,i)=>{"use strict";i.r(e),i.d(e,{GlobalError:()=>a.a,__next_app__:()=>d,pages:()=>h,routeModule:()=>c,tree:()=>u});var s=i(5239),n=i(8088),r=i(8170),a=i.n(r),o=i(893),l={};for(let t in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(l[t]=()=>o[t]);i.d(e,l);let u={children:["",{children:["(root)",{children:["interview",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,1079)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\interview\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,2528)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(i.bind(i,8014)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,h=["D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\interview\\page.tsx"],d={require:i,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(root)/interview/page",pathname:"/interview",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},9121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9587:()=>{}};var e=require("../../../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),s=e.X(0,[97,423,715,23,762],()=>i(7089));module.exports=s})();