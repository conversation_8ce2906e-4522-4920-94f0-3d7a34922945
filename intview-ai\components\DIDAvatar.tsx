"use client";
import React, { useState, useRef, useEffect } from "react";
import { Bot } from "lucide-react";
import { motion } from "framer-motion";
import { VideoPreloadingService } from "@/lib/videoPreloadingService";

const DID_API_URL = "https://api.d-id.com/talks";

interface DIDAvatarProps {
  text?: string;
  videoUrl?: string; // Pre-generated video URL
  onVideoReady?: () => void;
  onVideoEnd?: () => void;
  className?: string;
  isLoading?: boolean;
  preloadingService?: VideoPreloadingService;
  showPreloadingStatus?: boolean;
  priority?: number; // Priority for video generation
}

const DIDAvatar: React.FC<DIDAvatarProps> = ({
  text,
  videoUrl: preGeneratedVideoUrl,
  onVideoReady,
  onVideoEnd,
  className = "",
  preloadingService,
  showPreloadingStatus = false,
  priority = 0,
}) => {
  const [videoUrl, setVideoUrl] = useState<string | null>(preGeneratedVideoUrl || null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [, setPreloadingStatus] = useState<{
    pending: number;
    loading: number;
    completed: number;
    failed: number;
    total: number;
  } | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  const createTalk = async (inputText: string) => {
    if (!inputText.trim()) return;

    setLoading(true);
    setVideoUrl(null);
    setError(null);

    try {
      let videoUrlResponse: string | null = null;

      // Try to use preloading service first
      if (preloadingService) {
        try {
          videoUrlResponse = await preloadingService.getVideoUrl(inputText, priority);
        } catch (err) {
          console.warn("Failed to get video from preloading service, falling back to direct API:", err);
        }
      }

      // Fallback to direct API call if preloading service failed or not available
      if (!videoUrlResponse) {
        const payload = {
          script: {
            type: "text",
            input: inputText.trim(),
          },
          // source_url: "https://i.imgur.com/22232650.png",//
        };

        const response = await fetch(DID_API_URL, {
          method: "POST",
          headers: {
            Authorization: `Basic ${btoa(process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || "")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        });

        if (!response.ok) {
          throw new Error(`Failed to create talk: ${response.statusText}`);
        }

        const data = await response.json();
        const talkId = data.id;

        // Poll for video completion
        let attempts = 0;
        const maxAttempts = 30; // 90 seconds max wait time

        while (!videoUrlResponse && attempts < maxAttempts) {
          const pollResp = await fetch(`${DID_API_URL}/${talkId}`, {
            headers: {
              Authorization: `Basic ${btoa(process.env.NEXT_PUBLIC_DID_API_KEY || process.env.DID_API_KEY || "")}`,
              "Content-Type": "application/json",
            },
          });

          const pollData = await pollResp.json();

          if (pollData.status === "done" && pollData.result_url) {
            videoUrlResponse = pollData.result_url;
          } else if (pollData.status === "error") {
            throw new Error("Video generation failed");
          } else {
            await new Promise((resolve) => setTimeout(resolve, 3000));
            attempts++;
          }
        }

        if (!videoUrlResponse) {
          throw new Error("Video generation timed out");
        }
      }

      setVideoUrl(videoUrlResponse);
      onVideoReady?.();
    } catch (err: unknown) {
      console.error("D-ID API Error:", err);
      setError(err instanceof Error ? err.message : "Failed to generate video");
    } finally {
      setLoading(false);
    }
  };

  // Effect to handle preloading status updates
  useEffect(() => {
    if (preloadingService && showPreloadingStatus) {
      const interval = setInterval(() => {
        const status = preloadingService.getQueueStatus();
        setPreloadingStatus(status);
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [preloadingService, showPreloadingStatus]);

  // Effect to handle pre-generated video URL
  useEffect(() => {
    if (preGeneratedVideoUrl) {
      setVideoUrl(preGeneratedVideoUrl);
      setLoading(false);
      setError(null);
      // Don't call onVideoReady immediately, let the video element handle it
    }
  }, [preGeneratedVideoUrl]);

  // Effect to create video when text changes (only if no pre-generated URL)
  useEffect(() => {
    if (text && text.trim() && !preGeneratedVideoUrl) {
      createTalk(text);
    }
  }, [text, preGeneratedVideoUrl, createTalk]);

  // Handle video end event
  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      const handleVideoEnd = () => {
        onVideoEnd?.();
      };

      video.addEventListener("ended", handleVideoEnd);
      return () => {
        video.removeEventListener("ended", handleVideoEnd);
      };
    }
  }, [videoUrl, onVideoEnd]);

  return (
    <div className={`relative w-full h-full ${className}`}>
      {/* Always show the avatar container to prevent disappearing */}
      <div className="w-full h-full relative">
        {videoUrl && !loading && !error ? (
          <motion.div
            key={videoUrl} // Use videoUrl as key to trigger smooth transitions
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
            className="w-full h-full relative"
          >
            <video
              key={videoUrl} // Force re-render when URL changes
              ref={videoRef}
              src={videoUrl}
              autoPlay
              className="w-full h-full object-cover rounded-lg"
              onLoadedData={() => onVideoReady?.()}
            />
            {/* Speaking indicator */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="absolute top-4 right-4 flex items-center space-x-2 bg-black/20 backdrop-blur-sm rounded-full px-3 py-2"
            >
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-white text-sm font-medium">Speaking</span>
            </motion.div>


          </motion.div>
        ) : (
          <motion.div
            key="placeholder"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
            className="w-full h-full bg-gradient-to-br from-blue-400 via-indigo-500 to-purple-600 flex flex-col items-center justify-center relative rounded-lg"
          >
            {/* Large Bot Icon */}
            <Bot className="w-16 h-16 sm:w-24 sm:h-24 lg:w-32 lg:h-32 text-white/90 mb-4 lg:mb-8" />

            {/* Avatar Info */}
            <div className="text-center text-white px-4">
              <h3 className="text-lg sm:text-xl lg:text-2xl font-bold mb-1 lg:mb-2">
                AI Interviewer
              </h3>
              <div className="flex items-center justify-center space-x-2 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 lg:px-4 lg:py-2">
                <div className="w-2 h-2 lg:w-3 lg:h-3 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-white font-medium text-sm lg:text-base">
                  Ready
                </span>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute top-4 left-4 lg:top-8 lg:left-8 w-8 h-8 lg:w-16 lg:h-16 bg-white/10 rounded-full blur-xl"></div>
            <div className="absolute bottom-6 right-6 lg:bottom-12 lg:right-12 w-12 h-12 lg:w-24 lg:h-24 bg-white/5 rounded-full blur-2xl"></div>
            <div className="absolute top-1/3 right-4 lg:right-8 w-4 h-4 lg:w-8 lg:h-8 bg-white/20 rounded-full blur-sm"></div>
          </motion.div>
        )}
      </div>



      {/* Error State */}
      {error && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute inset-0 bg-red-50 flex flex-col items-center justify-center rounded-lg border-2 border-red-200"
        >
          <div className="text-center text-red-600 px-4">
            <h3 className="text-lg font-semibold mb-2">Error</h3>
            <p className="text-sm">{error}</p>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default DIDAvatar;
