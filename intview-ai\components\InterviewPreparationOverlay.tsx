"use client";
import React from "react";
import { motion } from "framer-motion";

interface InterviewPreparationOverlayProps {
  isVisible: boolean;
  progress: number; // 0-100
  currentStep: string;
  totalQuestions: number;
  loadedQuestions: number;
  failedQuestions: number;
  estimatedTimeRemaining?: number; // in seconds
}

const InterviewPreparationOverlay: React.FC<InterviewPreparationOverlayProps> = ({
  isVisible,
}) => {
  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white/95 backdrop-blur-sm rounded-2xl p-8 text-center shadow-2xl mx-4 max-w-md w-full"
      >
        {/* Simple Title */}
        <h3 className="text-xl font-bold text-gray-800">
          Preparing Interview
        </h3>
      </motion.div>
    </motion.div>
  );
};

export default InterviewPreparationOverlay;
