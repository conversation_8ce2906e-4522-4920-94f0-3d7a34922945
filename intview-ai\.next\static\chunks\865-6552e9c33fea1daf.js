(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[865],{1362:(t,e,a)=>{"use strict";a.d(e,{D:()=>d,N:()=>c});var n=a(2115),r=(t,e,a,n,r,s,o,i)=>{let l=document.documentElement,d=["light","dark"];function c(e){var a;(Array.isArray(t)?t:[t]).forEach(t=>{let a="class"===t,n=a&&s?r.map(t=>s[t]||t):r;a?(l.classList.remove(...n),l.classList.add(s&&s[e]?s[e]:e)):l.setAttribute(t,e)}),a=e,i&&d.includes(a)&&(l.style.colorScheme=a)}if(n)c(n);else try{let t=localStorage.getItem(e)||a,n=o&&"system"===t?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":t;c(n)}catch(t){}},s=["light","dark"],o="(prefers-color-scheme: dark)",i=n.createContext(void 0),l={setTheme:t=>{},themes:[]},d=()=>{var t;return null!=(t=n.useContext(i))?t:l},c=t=>n.useContext(i)?n.createElement(n.Fragment,null,t.children):n.createElement(m,{...t}),u=["light","dark"],m=t=>{let{forcedTheme:e,disableTransitionOnChange:a=!1,enableSystem:r=!0,enableColorScheme:l=!0,storageKey:d="theme",themes:c=u,defaultTheme:m=r?"system":"light",attribute:v="data-theme",value:y,children:b,nonce:w,scriptProps:x}=t,[E,k]=n.useState(()=>f(d,m)),[S,T]=n.useState(()=>"system"===E?g():E),N=y?Object.values(y):c,C=n.useCallback(t=>{let e=t;if(!e)return;"system"===t&&r&&(e=g());let n=y?y[e]:e,o=a?h(w):null,i=document.documentElement,d=t=>{"class"===t?(i.classList.remove(...N),n&&i.classList.add(n)):t.startsWith("data-")&&(n?i.setAttribute(t,n):i.removeAttribute(t))};if(Array.isArray(v)?v.forEach(d):d(v),l){let t=s.includes(m)?m:null,a=s.includes(e)?e:t;i.style.colorScheme=a}null==o||o()},[w]),M=n.useCallback(t=>{let e="function"==typeof t?t(E):t;k(e);try{localStorage.setItem(d,e)}catch(t){}},[E]),L=n.useCallback(t=>{T(g(t)),"system"===E&&r&&!e&&C("system")},[E,e]);n.useEffect(()=>{let t=window.matchMedia(o);return t.addListener(L),L(t),()=>t.removeListener(L)},[L]),n.useEffect(()=>{let t=t=>{t.key===d&&(t.newValue?k(t.newValue):M(m))};return window.addEventListener("storage",t),()=>window.removeEventListener("storage",t)},[M]),n.useEffect(()=>{C(null!=e?e:E)},[e,E]);let _=n.useMemo(()=>({theme:E,setTheme:M,forcedTheme:e,resolvedTheme:"system"===E?S:E,themes:r?[...c,"system"]:c,systemTheme:r?S:void 0}),[E,M,e,S,r,c]);return n.createElement(i.Provider,{value:_},n.createElement(p,{forcedTheme:e,storageKey:d,attribute:v,enableSystem:r,enableColorScheme:l,defaultTheme:m,value:y,themes:c,nonce:w,scriptProps:x}),b)},p=n.memo(t=>{let{forcedTheme:e,storageKey:a,attribute:s,enableSystem:o,enableColorScheme:i,defaultTheme:l,value:d,themes:c,nonce:u,scriptProps:m}=t,p=JSON.stringify([s,a,l,e,c,d,o,i]).slice(1,-1);return n.createElement("script",{...m,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(r.toString(),")(").concat(p,")")}})}),f=(t,e)=>{let a;try{a=localStorage.getItem(t)||void 0}catch(t){}return a||e},h=t=>{let e=document.createElement("style");return t&&e.setAttribute("nonce",t),e.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},g=t=>(t||(t=window.matchMedia(o)),t.matches?"dark":"light")},4993:t=>{t.exports={style:{fontFamily:"'spaceGrotesk', 'spaceGrotesk Fallback'"},className:"__className_fc28aa",variable:"__variable_fc28aa"}},5493:(t,e,a)=>{"use strict";a.d(e,{SessionProvider:()=>ts});var n,r,s,o,i,l=a(5155),d=a(2115),c=a.t(d,2);class u extends Error{constructor(t,e){t instanceof Error?super(void 0,{cause:{err:t,...t.cause,...e}}):"string"==typeof t?(e instanceof Error&&(e={err:e,...e.cause}),super(t,e)):super(void 0,t),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let a=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${a}`}}class m extends u{}m.kind="signIn";class p extends u{}p.type="AdapterError";class f extends u{}f.type="AccessDenied";class h extends u{}h.type="CallbackRouteError";class g extends u{}g.type="ErrorPageLoop";class v extends u{}v.type="EventError";class y extends u{}y.type="InvalidCallbackUrl";class b extends m{constructor(){super(...arguments),this.code="credentials"}}b.type="CredentialsSignin";class w extends u{}w.type="InvalidEndpoints";class x extends u{}x.type="InvalidCheck";class E extends u{}E.type="JWTSessionError";class k extends u{}k.type="MissingAdapter";class S extends u{}S.type="MissingAdapterMethods";class T extends u{}T.type="MissingAuthorize";class N extends u{}N.type="MissingSecret";class C extends m{}C.type="OAuthAccountNotLinked";class M extends m{}M.type="OAuthCallbackError";class L extends u{}L.type="OAuthProfileParseError";class _ extends u{}_.type="SessionTokenError";class A extends m{}A.type="OAuthSignInError";class R extends m{}R.type="EmailSignInError";class B extends u{}B.type="SignOutError";class P extends u{}P.type="UnknownAction";class I extends u{}I.type="UnsupportedStrategy";class j extends u{}j.type="InvalidProvider";class z extends u{}z.type="UntrustedHost";class U extends u{}U.type="Verification";class D extends m{}D.type="MissingCSRF";class H extends u{}H.type="DuplicateConditionalUI";class Y extends u{}Y.type="MissingWebAuthnAutocomplete";class V extends u{}V.type="WebAuthnVerificationError";class O extends m{}O.type="AccountNotLinked";class X extends u{}X.type="ExperimentalFeatureNotEnabled";class F extends u{}class W extends u{}async function K(t,e,a){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};let r="".concat(e.basePath,"/").concat(t);try{var s;let t={headers:{"Content-Type":"application/json",...(null==n||null==(s=n.headers)?void 0:s.cookie)?{cookie:n.headers.cookie}:{}}};(null==n?void 0:n.body)&&(t.body=JSON.stringify(n.body),t.method="POST");let e=await fetch(r,t),a=await e.json();if(!e.ok)throw a;return a}catch(t){return a.error(new F(t.message,t)),null}}function $(){return Math.floor(Date.now()/1e3)}function J(t){let e=new URL("http://localhost:3000/api/auth");t&&!t.startsWith("http")&&(t="https://".concat(t));let a=new URL(t||e),n=("/"===a.pathname?e.pathname:a.pathname).replace(/\/$/,""),r="".concat(a.origin).concat(n);return{origin:a.origin,host:a.host,path:n,base:r,toString:()=>r}}var q=a(9509);let G={baseUrl:J(null!=(r=q.env.NEXTAUTH_URL)?r:q.env.VERCEL_URL).origin,basePath:J(q.env.NEXTAUTH_URL).path,baseUrlServer:J(null!=(o=null!=(s=q.env.NEXTAUTH_URL_INTERNAL)?s:q.env.NEXTAUTH_URL)?o:q.env.VERCEL_URL).origin,basePathServer:J(null!=(i=q.env.NEXTAUTH_URL_INTERNAL)?i:q.env.NEXTAUTH_URL).path,_lastSync:0,_session:void 0,_getSession:()=>{}},Q=null;function Z(){return"undefined"==typeof BroadcastChannel?{postMessage:()=>{},addEventListener:()=>{},removeEventListener:()=>{},name:"next-auth",onmessage:null,onmessageerror:null,close:()=>{},dispatchEvent:()=>!1}:new BroadcastChannel("next-auth")}function tt(){return null===Q&&(Q=Z()),Q}let te={debug:console.debug,error:console.error,warn:console.warn},ta=null==(n=d.createContext)?void 0:n.call(c,void 0);async function tn(t){var e;let a=await K("session",G,te,t);return(null==(e=null==t?void 0:t.broadcast)||e)&&Z().postMessage({event:"session",data:{trigger:"getSession"}}),a}async function tr(){var t;let e=await K("csrf",G,te);return null!=(t=null==e?void 0:e.csrfToken)?t:""}function ts(t){if(!ta)throw Error("React Context is unavailable in Server Components");let{children:e,basePath:a,refetchInterval:n,refetchWhenOffline:r}=t;a&&(G.basePath=a);let s=void 0!==t.session;G._lastSync=s?$():0;let[o,i]=d.useState(()=>(s&&(G._session=t.session),t.session)),[c,u]=d.useState(!s);d.useEffect(()=>(G._getSession=async function(){let{event:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let e="storage"===t;if(e||void 0===G._session){G._lastSync=$(),G._session=await tn({broadcast:!e}),i(G._session);return}if(!t||null===G._session||$()<G._lastSync)return;G._lastSync=$(),G._session=await tn(),i(G._session)}catch(t){te.error(new W(t.message,t))}finally{u(!1)}},G._getSession(),()=>{G._lastSync=0,G._session=void 0,G._getSession=()=>{}}),[]),d.useEffect(()=>{let t=()=>G._getSession({event:"storage"});return tt().addEventListener("message",t),()=>tt().removeEventListener("message",t)},[]),d.useEffect(()=>{let{refetchOnWindowFocus:e=!0}=t,a=()=>{e&&"visible"===document.visibilityState&&G._getSession({event:"visibilitychange"})};return document.addEventListener("visibilitychange",a,!1),()=>document.removeEventListener("visibilitychange",a,!1)},[t.refetchOnWindowFocus]);let m=function(){let[t,e]=d.useState("undefined"!=typeof navigator&&navigator.onLine),a=()=>e(!0),n=()=>e(!1);return d.useEffect(()=>(window.addEventListener("online",a),window.addEventListener("offline",n),()=>{window.removeEventListener("online",a),window.removeEventListener("offline",n)}),[]),t}(),p=!1!==r||m;d.useEffect(()=>{if(n&&p){let t=setInterval(()=>{G._session&&G._getSession({event:"poll"})},1e3*n);return()=>clearInterval(t)}},[n,p]);let f=d.useMemo(()=>({data:o,status:c?"loading":o?"authenticated":"unauthenticated",async update(t){if(c)return;u(!0);let e=await K("session",G,te,void 0===t?void 0:{body:{csrfToken:await tr(),data:t}});return u(!1),e&&(i(e),tt().postMessage({event:"session",data:{trigger:"getSession"}})),e}}),[o,c]);return(0,l.jsx)(ta.Provider,{value:f,children:e})}},6671:(t,e,a)=>{"use strict";a.d(e,{l$:()=>E});var n=a(2115),r=a(7650);let s=t=>{switch(t){case"success":return l;case"info":return c;case"warning":return d;case"error":return u;default:return null}},o=Array(12).fill(0),i=t=>{let{visible:e,className:a}=t;return n.createElement("div",{className:["sonner-loading-wrapper",a].filter(Boolean).join(" "),"data-visible":e},n.createElement("div",{className:"sonner-spinner"},o.map((t,e)=>n.createElement("div",{className:"sonner-loading-bar",key:"spinner-bar-".concat(e)}))))},l=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),d=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),c=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),u=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),m=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},n.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),n.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),p=()=>{let[t,e]=n.useState(document.hidden);return n.useEffect(()=>{let t=()=>{e(document.hidden)};return document.addEventListener("visibilitychange",t),()=>window.removeEventListener("visibilitychange",t)},[]),t},f=1;class h{constructor(){this.subscribe=t=>(this.subscribers.push(t),()=>{let e=this.subscribers.indexOf(t);this.subscribers.splice(e,1)}),this.publish=t=>{this.subscribers.forEach(e=>e(t))},this.addToast=t=>{this.publish(t),this.toasts=[...this.toasts,t]},this.create=t=>{var e;let{message:a,...n}=t,r="number"==typeof(null==t?void 0:t.id)||(null==(e=t.id)?void 0:e.length)>0?t.id:f++,s=this.toasts.find(t=>t.id===r),o=void 0===t.dismissible||t.dismissible;return this.dismissedToasts.has(r)&&this.dismissedToasts.delete(r),s?this.toasts=this.toasts.map(e=>e.id===r?(this.publish({...e,...t,id:r,title:a}),{...e,...t,id:r,dismissible:o,title:a}):e):this.addToast({title:a,...n,dismissible:o,id:r}),r},this.dismiss=t=>(t?(this.dismissedToasts.add(t),requestAnimationFrame(()=>this.subscribers.forEach(e=>e({id:t,dismiss:!0})))):this.toasts.forEach(t=>{this.subscribers.forEach(e=>e({id:t.id,dismiss:!0}))}),t),this.message=(t,e)=>this.create({...e,message:t}),this.error=(t,e)=>this.create({...e,message:t,type:"error"}),this.success=(t,e)=>this.create({...e,type:"success",message:t}),this.info=(t,e)=>this.create({...e,type:"info",message:t}),this.warning=(t,e)=>this.create({...e,type:"warning",message:t}),this.loading=(t,e)=>this.create({...e,type:"loading",message:t}),this.promise=(t,e)=>{let a,r;if(!e)return;void 0!==e.loading&&(r=this.create({...e,promise:t,type:"loading",message:e.loading,description:"function"!=typeof e.description?e.description:void 0}));let s=Promise.resolve(t instanceof Function?t():t),o=void 0!==r,i=s.then(async t=>{if(a=["resolve",t],n.isValidElement(t))o=!1,this.create({id:r,type:"default",message:t});else if(v(t)&&!t.ok){o=!1;let a="function"==typeof e.error?await e.error("HTTP error! status: ".concat(t.status)):e.error,s="function"==typeof e.description?await e.description("HTTP error! status: ".concat(t.status)):e.description,i="object"!=typeof a||n.isValidElement(a)?{message:a}:a;this.create({id:r,type:"error",description:s,...i})}else if(t instanceof Error){o=!1;let a="function"==typeof e.error?await e.error(t):e.error,s="function"==typeof e.description?await e.description(t):e.description,i="object"!=typeof a||n.isValidElement(a)?{message:a}:a;this.create({id:r,type:"error",description:s,...i})}else if(void 0!==e.success){o=!1;let a="function"==typeof e.success?await e.success(t):e.success,s="function"==typeof e.description?await e.description(t):e.description,i="object"!=typeof a||n.isValidElement(a)?{message:a}:a;this.create({id:r,type:"success",description:s,...i})}}).catch(async t=>{if(a=["reject",t],void 0!==e.error){o=!1;let a="function"==typeof e.error?await e.error(t):e.error,s="function"==typeof e.description?await e.description(t):e.description,i="object"!=typeof a||n.isValidElement(a)?{message:a}:a;this.create({id:r,type:"error",description:s,...i})}}).finally(()=>{o&&(this.dismiss(r),r=void 0),null==e.finally||e.finally.call(e)}),l=()=>new Promise((t,e)=>i.then(()=>"reject"===a[0]?e(a[1]):t(a[1])).catch(e));return"string"!=typeof r&&"number"!=typeof r?{unwrap:l}:Object.assign(r,{unwrap:l})},this.custom=(t,e)=>{let a=(null==e?void 0:e.id)||f++;return this.create({jsx:t(a),id:a,...e}),a},this.getActiveToasts=()=>this.toasts.filter(t=>!this.dismissedToasts.has(t.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}}let g=new h,v=t=>t&&"object"==typeof t&&"ok"in t&&"boolean"==typeof t.ok&&"status"in t&&"number"==typeof t.status;function y(t){return void 0!==t.label}function b(){for(var t=arguments.length,e=Array(t),a=0;a<t;a++)e[a]=arguments[a];return e.filter(Boolean).join(" ")}Object.assign((t,e)=>{let a=(null==e?void 0:e.id)||f++;return g.addToast({title:t,...e,id:a}),a},{success:g.success,info:g.info,warning:g.warning,error:g.error,custom:g.custom,message:g.message,promise:g.promise,dismiss:g.dismiss,loading:g.loading},{getHistory:()=>g.toasts,getToasts:()=>g.getActiveToasts()}),function(t){if(!t||"undefined"==typeof document)return;let e=document.head||document.getElementsByTagName("head")[0],a=document.createElement("style");a.type="text/css",e.appendChild(a),a.styleSheet?a.styleSheet.cssText=t:a.appendChild(document.createTextNode(t))}("[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}");let w=t=>{var e,a,r,o,l,d,c,u,f,h,g;let{invert:v,toast:w,unstyled:x,interacting:E,setHeights:k,visibleToasts:S,heights:T,index:N,toasts:C,expanded:M,removeToast:L,defaultRichColors:_,closeButton:A,style:R,cancelButtonStyle:B,actionButtonStyle:P,className:I="",descriptionClassName:j="",duration:z,position:U,gap:D,expandByDefault:H,classNames:Y,icons:V,closeButtonAriaLabel:O="Close toast"}=t,[X,F]=n.useState(null),[W,K]=n.useState(null),[$,J]=n.useState(!1),[q,G]=n.useState(!1),[Q,Z]=n.useState(!1),[tt,te]=n.useState(!1),[ta,tn]=n.useState(!1),[tr,ts]=n.useState(0),[to,ti]=n.useState(0),tl=n.useRef(w.duration||z||4e3),td=n.useRef(null),tc=n.useRef(null),tu=0===N,tm=N+1<=S,tp=w.type,tf=!1!==w.dismissible,th=w.className||"",tg=w.descriptionClassName||"",tv=n.useMemo(()=>T.findIndex(t=>t.toastId===w.id)||0,[T,w.id]),ty=n.useMemo(()=>{var t;return null!=(t=w.closeButton)?t:A},[w.closeButton,A]),tb=n.useMemo(()=>w.duration||z||4e3,[w.duration,z]),tw=n.useRef(0),tx=n.useRef(0),tE=n.useRef(0),tk=n.useRef(null),[tS,tT]=U.split("-"),tN=n.useMemo(()=>T.reduce((t,e,a)=>a>=tv?t:t+e.height,0),[T,tv]),tC=p(),tM=w.invert||v,tL="loading"===tp;tx.current=n.useMemo(()=>tv*D+tN,[tv,tN]),n.useEffect(()=>{tl.current=tb},[tb]),n.useEffect(()=>{J(!0)},[]),n.useEffect(()=>{let t=tc.current;if(t){let e=t.getBoundingClientRect().height;return ti(e),k(t=>[{toastId:w.id,height:e,position:w.position},...t]),()=>k(t=>t.filter(t=>t.toastId!==w.id))}},[k,w.id]),n.useLayoutEffect(()=>{if(!$)return;let t=tc.current,e=t.style.height;t.style.height="auto";let a=t.getBoundingClientRect().height;t.style.height=e,ti(a),k(t=>t.find(t=>t.toastId===w.id)?t.map(t=>t.toastId===w.id?{...t,height:a}:t):[{toastId:w.id,height:a,position:w.position},...t])},[$,w.title,w.description,k,w.id,w.jsx,w.action,w.cancel]);let t_=n.useCallback(()=>{G(!0),ts(tx.current),k(t=>t.filter(t=>t.toastId!==w.id)),setTimeout(()=>{L(w)},200)},[w,L,k,tx]);n.useEffect(()=>{let t;if((!w.promise||"loading"!==tp)&&w.duration!==1/0&&"loading"!==w.type)return M||E||tC?(()=>{if(tE.current<tw.current){let t=new Date().getTime()-tw.current;tl.current=tl.current-t}tE.current=new Date().getTime()})():tl.current!==1/0&&(tw.current=new Date().getTime(),t=setTimeout(()=>{null==w.onAutoClose||w.onAutoClose.call(w,w),t_()},tl.current)),()=>clearTimeout(t)},[M,E,w,tp,tC,t_]),n.useEffect(()=>{w.delete&&(t_(),null==w.onDismiss||w.onDismiss.call(w,w))},[t_,w.delete]);let tA=w.icon||(null==V?void 0:V[tp])||s(tp);return n.createElement("li",{tabIndex:0,ref:tc,className:b(I,th,null==Y?void 0:Y.toast,null==w||null==(e=w.classNames)?void 0:e.toast,null==Y?void 0:Y.default,null==Y?void 0:Y[tp],null==w||null==(a=w.classNames)?void 0:a[tp]),"data-sonner-toast":"","data-rich-colors":null!=(h=w.richColors)?h:_,"data-styled":!(w.jsx||w.unstyled||x),"data-mounted":$,"data-promise":!!w.promise,"data-swiped":ta,"data-removed":q,"data-visible":tm,"data-y-position":tS,"data-x-position":tT,"data-index":N,"data-front":tu,"data-swiping":Q,"data-dismissible":tf,"data-type":tp,"data-invert":tM,"data-swipe-out":tt,"data-swipe-direction":W,"data-expanded":!!(M||H&&$),style:{"--index":N,"--toasts-before":N,"--z-index":C.length-N,"--offset":"".concat(q?tr:tx.current,"px"),"--initial-height":H?"auto":"".concat(to,"px"),...R,...w.style},onDragEnd:()=>{Z(!1),F(null),tk.current=null},onPointerDown:t=>{2!==t.button&&!tL&&tf&&(td.current=new Date,ts(tx.current),t.target.setPointerCapture(t.pointerId),"BUTTON"!==t.target.tagName&&(Z(!0),tk.current={x:t.clientX,y:t.clientY}))},onPointerUp:()=>{var t,e,a,n,r;if(tt||!tf)return;tk.current=null;let s=Number((null==(t=tc.current)?void 0:t.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),o=Number((null==(e=tc.current)?void 0:e.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),i=new Date().getTime()-(null==(a=td.current)?void 0:a.getTime()),l="x"===X?s:o,d=Math.abs(l)/i;if(Math.abs(l)>=45||d>.11){ts(tx.current),null==w.onDismiss||w.onDismiss.call(w,w),"x"===X?K(s>0?"right":"left"):K(o>0?"down":"up"),t_(),te(!0);return}null==(n=tc.current)||n.style.setProperty("--swipe-amount-x","0px"),null==(r=tc.current)||r.style.setProperty("--swipe-amount-y","0px"),tn(!1),Z(!1),F(null)},onPointerMove:e=>{var a,n,r,s;if(!tk.current||!tf||(null==(a=window.getSelection())?void 0:a.toString().length)>0)return;let o=e.clientY-tk.current.y,i=e.clientX-tk.current.x,l=null!=(s=t.swipeDirections)?s:function(t){let[e,a]=t.split("-"),n=[];return e&&n.push(e),a&&n.push(a),n}(U);!X&&(Math.abs(i)>1||Math.abs(o)>1)&&F(Math.abs(i)>Math.abs(o)?"x":"y");let d={x:0,y:0},c=t=>1/(1.5+Math.abs(t)/20);if("y"===X){if(l.includes("top")||l.includes("bottom"))if(l.includes("top")&&o<0||l.includes("bottom")&&o>0)d.y=o;else{let t=o*c(o);d.y=Math.abs(t)<Math.abs(o)?t:o}}else if("x"===X&&(l.includes("left")||l.includes("right")))if(l.includes("left")&&i<0||l.includes("right")&&i>0)d.x=i;else{let t=i*c(i);d.x=Math.abs(t)<Math.abs(i)?t:i}(Math.abs(d.x)>0||Math.abs(d.y)>0)&&tn(!0),null==(n=tc.current)||n.style.setProperty("--swipe-amount-x","".concat(d.x,"px")),null==(r=tc.current)||r.style.setProperty("--swipe-amount-y","".concat(d.y,"px"))}},ty&&!w.jsx&&"loading"!==tp?n.createElement("button",{"aria-label":O,"data-disabled":tL,"data-close-button":!0,onClick:tL||!tf?()=>{}:()=>{t_(),null==w.onDismiss||w.onDismiss.call(w,w)},className:b(null==Y?void 0:Y.closeButton,null==w||null==(r=w.classNames)?void 0:r.closeButton)},null!=(g=null==V?void 0:V.close)?g:m):null,(tp||w.icon||w.promise)&&null!==w.icon&&((null==V?void 0:V[tp])!==null||w.icon)?n.createElement("div",{"data-icon":"",className:b(null==Y?void 0:Y.icon,null==w||null==(o=w.classNames)?void 0:o.icon)},w.promise||"loading"===w.type&&!w.icon?w.icon||function(){var t,e;return(null==V?void 0:V.loading)?n.createElement("div",{className:b(null==Y?void 0:Y.loader,null==w||null==(e=w.classNames)?void 0:e.loader,"sonner-loader"),"data-visible":"loading"===tp},V.loading):n.createElement(i,{className:b(null==Y?void 0:Y.loader,null==w||null==(t=w.classNames)?void 0:t.loader),visible:"loading"===tp})}():null,"loading"!==w.type?tA:null):null,n.createElement("div",{"data-content":"",className:b(null==Y?void 0:Y.content,null==w||null==(l=w.classNames)?void 0:l.content)},n.createElement("div",{"data-title":"",className:b(null==Y?void 0:Y.title,null==w||null==(d=w.classNames)?void 0:d.title)},w.jsx?w.jsx:"function"==typeof w.title?w.title():w.title),w.description?n.createElement("div",{"data-description":"",className:b(j,tg,null==Y?void 0:Y.description,null==w||null==(c=w.classNames)?void 0:c.description)},"function"==typeof w.description?w.description():w.description):null),n.isValidElement(w.cancel)?w.cancel:w.cancel&&y(w.cancel)?n.createElement("button",{"data-button":!0,"data-cancel":!0,style:w.cancelButtonStyle||B,onClick:t=>{y(w.cancel)&&tf&&(null==w.cancel.onClick||w.cancel.onClick.call(w.cancel,t),t_())},className:b(null==Y?void 0:Y.cancelButton,null==w||null==(u=w.classNames)?void 0:u.cancelButton)},w.cancel.label):null,n.isValidElement(w.action)?w.action:w.action&&y(w.action)?n.createElement("button",{"data-button":!0,"data-action":!0,style:w.actionButtonStyle||P,onClick:t=>{y(w.action)&&(null==w.action.onClick||w.action.onClick.call(w.action,t),t.defaultPrevented||t_())},className:b(null==Y?void 0:Y.actionButton,null==w||null==(f=w.classNames)?void 0:f.actionButton)},w.action.label):null)};function x(){if("undefined"==typeof window||"undefined"==typeof document)return"ltr";let t=document.documentElement.getAttribute("dir");return"auto"!==t&&t?t:window.getComputedStyle(document.documentElement).direction}let E=n.forwardRef(function(t,e){let{invert:a,position:s="bottom-right",hotkey:o=["altKey","KeyT"],expand:i,closeButton:l,className:d,offset:c,mobileOffset:u,theme:m="light",richColors:p,duration:f,style:h,visibleToasts:v=3,toastOptions:y,dir:b=x(),gap:E=14,icons:k,containerAriaLabel:S="Notifications"}=t,[T,N]=n.useState([]),C=n.useMemo(()=>Array.from(new Set([s].concat(T.filter(t=>t.position).map(t=>t.position)))),[T,s]),[M,L]=n.useState([]),[_,A]=n.useState(!1),[R,B]=n.useState(!1),[P,I]=n.useState("system"!==m?m:"undefined"!=typeof window&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),j=n.useRef(null),z=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),U=n.useRef(null),D=n.useRef(!1),H=n.useCallback(t=>{N(e=>{var a;return(null==(a=e.find(e=>e.id===t.id))?void 0:a.delete)||g.dismiss(t.id),e.filter(e=>{let{id:a}=e;return a!==t.id})})},[]);return n.useEffect(()=>g.subscribe(t=>{if(t.dismiss)return void requestAnimationFrame(()=>{N(e=>e.map(e=>e.id===t.id?{...e,delete:!0}:e))});setTimeout(()=>{r.flushSync(()=>{N(e=>{let a=e.findIndex(e=>e.id===t.id);return -1!==a?[...e.slice(0,a),{...e[a],...t},...e.slice(a+1)]:[t,...e]})})})}),[T]),n.useEffect(()=>{if("system"!==m)return void I(m);if("system"===m&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?I("dark"):I("light")),"undefined"==typeof window)return;let t=window.matchMedia("(prefers-color-scheme: dark)");try{t.addEventListener("change",t=>{let{matches:e}=t;e?I("dark"):I("light")})}catch(e){t.addListener(t=>{let{matches:e}=t;try{e?I("dark"):I("light")}catch(t){console.error(t)}})}},[m]),n.useEffect(()=>{T.length<=1&&A(!1)},[T]),n.useEffect(()=>{let t=t=>{var e,a;o.every(e=>t[e]||t.code===e)&&(A(!0),null==(a=j.current)||a.focus()),"Escape"===t.code&&(document.activeElement===j.current||(null==(e=j.current)?void 0:e.contains(document.activeElement)))&&A(!1)};return document.addEventListener("keydown",t),()=>document.removeEventListener("keydown",t)},[o]),n.useEffect(()=>{if(j.current)return()=>{U.current&&(U.current.focus({preventScroll:!0}),U.current=null,D.current=!1)}},[j.current]),n.createElement("section",{ref:e,"aria-label":"".concat(S," ").concat(z),tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},C.map((e,r)=>{var s;let[o,m]=e.split("-");return T.length?n.createElement("ol",{key:e,dir:"auto"===b?x():b,tabIndex:-1,ref:j,className:d,"data-sonner-toaster":!0,"data-sonner-theme":P,"data-y-position":o,"data-x-position":m,style:{"--front-toast-height":"".concat((null==(s=M[0])?void 0:s.height)||0,"px"),"--width":"".concat(356,"px"),"--gap":"".concat(E,"px"),...h,...function(t,e){let a={};return[t,e].forEach((t,e)=>{let n=1===e,r=n?"--mobile-offset":"--offset",s=n?"16px":"24px";function o(t){["top","right","bottom","left"].forEach(e=>{a["".concat(r,"-").concat(e)]="number"==typeof t?"".concat(t,"px"):t})}"number"==typeof t||"string"==typeof t?o(t):"object"==typeof t?["top","right","bottom","left"].forEach(e=>{void 0===t[e]?a["".concat(r,"-").concat(e)]=s:a["".concat(r,"-").concat(e)]="number"==typeof t[e]?"".concat(t[e],"px"):t[e]}):o(s)}),a}(c,u)},onBlur:t=>{D.current&&!t.currentTarget.contains(t.relatedTarget)&&(D.current=!1,U.current&&(U.current.focus({preventScroll:!0}),U.current=null))},onFocus:t=>{!(t.target instanceof HTMLElement&&"false"===t.target.dataset.dismissible)&&(D.current||(D.current=!0,U.current=t.relatedTarget))},onMouseEnter:()=>A(!0),onMouseMove:()=>A(!0),onMouseLeave:()=>{R||A(!1)},onDragEnd:()=>A(!1),onPointerDown:t=>{t.target instanceof HTMLElement&&"false"===t.target.dataset.dismissible||B(!0)},onPointerUp:()=>B(!1)},T.filter(t=>!t.position&&0===r||t.position===e).map((r,s)=>{var o,d;return n.createElement(w,{key:r.id,icons:k,index:s,toast:r,defaultRichColors:p,duration:null!=(o=null==y?void 0:y.duration)?o:f,className:null==y?void 0:y.className,descriptionClassName:null==y?void 0:y.descriptionClassName,invert:a,visibleToasts:v,closeButton:null!=(d=null==y?void 0:y.closeButton)?d:l,interacting:R,position:e,style:null==y?void 0:y.style,unstyled:null==y?void 0:y.unstyled,classNames:null==y?void 0:y.classNames,cancelButtonStyle:null==y?void 0:y.cancelButtonStyle,actionButtonStyle:null==y?void 0:y.actionButtonStyle,closeButtonAriaLabel:null==y?void 0:y.closeButtonAriaLabel,removeToast:H,toasts:T.filter(t=>t.position==r.position),heights:M.filter(t=>t.position==r.position),setHeights:L,expandByDefault:i,gap:E,expanded:_,swipeDirections:t.swipeDirections})})):null}))})},9623:t=>{t.exports={style:{fontFamily:"'Poppins', 'Poppins Fallback', Helvetica, Arial, sans-serif",fontStyle:"normal"},className:"__className_9b9fd1",variable:"__variable_9b9fd1"}}}]);