exports.id=717,exports.ids=[717],exports.modules={25:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\context\\\\Theme.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\context\\Theme.tsx","default")},363:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\sonner.tsx","Toaster")},1279:(e,r,t)=>{"use strict";t.d(r,{default:()=>n});var s=t(687);t(3210);var a=t(218);let n=({children:e,...r})=>(0,s.jsx)(a.N,{...r,children:e})},1788:(e,r,t)=>{"use strict";t.d(r,{A:()=>k});var s=t(687),a=t(3442),n=t(7605),i=t(4934),o=t(3210),l=t(1391),d=t(6241),c=t(9467);function m({className:e,...r}){return(0,s.jsx)(c.b,{"data-slot":"label",className:(0,d.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}let u=n.Op,h=o.createContext({}),p=({...e})=>(0,s.jsx)(h.Provider,{value:{name:e.name},children:(0,s.jsx)(n.xI,{...e})}),v=()=>{let e=o.useContext(h),r=o.useContext(g),{getFieldState:t}=(0,n.xW)(),s=(0,n.lN)({name:e.name}),a=t(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=r;return{id:i,name:e.name,formItemId:`${i}-form-item`,formDescriptionId:`${i}-form-item-description`,formMessageId:`${i}-form-item-message`,...a}},g=o.createContext({});function x({className:e,...r}){let t=o.useId();return(0,s.jsx)(g.Provider,{value:{id:t},children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,d.cn)("grid gap-2",e),...r})})}function b({className:e,...r}){let{error:t,formItemId:a}=v();return(0,s.jsx)(m,{"data-slot":"form-label","data-error":!!t,className:(0,d.cn)("data-[error=true]:text-destructive",e),htmlFor:a,...r})}function f({...e}){let{error:r,formItemId:t,formDescriptionId:a,formMessageId:n}=v();return(0,s.jsx)(l.DX,{"data-slot":"form-control",id:t,"aria-describedby":r?`${a} ${n}`:`${a}`,"aria-invalid":!!r,...e})}function w({className:e,...r}){let{error:t,formMessageId:a}=v(),n=t?String(t?.message??""):r.children;return n?(0,s.jsx)("p",{"data-slot":"form-message",id:a,className:(0,d.cn)("text-destructive text-sm",e),...r,children:n}):null}function j({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,d.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}var P=t(5814),y=t.n(P);let N={SIGN_IN:"/sign-in",SIGN_UP:"/sign-up"},k=({schema:e,defaultValues:r,formType:t})=>{let o=(0,n.mN)({resolver:(0,a.u)(e),defaultValues:r}),l=async()=>{},d="SIGN_IN"===t?"Sign In":"Sign Up";return(0,s.jsx)(u,{...o,children:(0,s.jsxs)("form",{onSubmit:o.handleSubmit(l),className:"mt-10 space-y-6",children:[Object.keys(r).map(e=>(0,s.jsx)(p,{control:o.control,name:e,render:({field:e})=>(0,s.jsxs)(x,{className:"flex w-full flex-col gap-2.5 ",children:[(0,s.jsx)(b,{className:"paragraph-medium text-dark400_light700",children:"email"===e.name?"Email Address":e.name.charAt(0).toUpperCase()+e.name.slice(1)}),(0,s.jsx)(f,{children:(0,s.jsx)(j,{type:"password"===e.name?"password":"text",...e,className:"paragraph-regular background-light900_dark300   light-border-2 text-dark300_light700 no-focus min-h-12   rounded-1.5 border"})}),(0,s.jsx)(w,{})]})},e)),(0,s.jsx)(i.$,{className:"primary-button paragraph-medium w-full   min-h-12 rounded-4xl px-4 py-3 font-inter !text-light-900   ",disabled:o.formState.isSubmitting,children:o.formState.isSubmitting?"Sign In"===d?"Signin In...":"Signin Up...":d}),"SIGN_IN"===t?(0,s.jsxs)("p",{children:["Don't have an account? ",(0,s.jsx)(y(),{href:N.SIGN_UP,className:"paragraph-semibold primary-text",children:"Sign Up"})]}):(0,s.jsxs)("p",{children:["Already have an account?"," ",(0,s.jsx)(y(),{href:N.SIGN_IN,className:"paragraph-semibold primary-text",children:"Sign In"})]})]})})}},2704:()=>{},2910:(e,r,t)=>{Promise.resolve().then(t.bind(t,363)),Promise.resolve().then(t.bind(t,25)),Promise.resolve().then(t.bind(t,2175))},4013:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},4593:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>i});var s=t(687),a=t(218),n=t(2581);let i=({...e})=>{let{theme:r="system"}=(0,a.D)();return(0,s.jsx)(n.l$,{theme:r,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},4934:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var s=t(687);t(3210);var a=t(1391),n=t(4224),i=t(6241);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:r,size:t,asChild:n=!1,...l}){let d=n?a.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:r,size:t,className:e})),...l})}},5896:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\forms\\\\SocialAuthForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\components\\forms\\SocialAuthForm.tsx","default")},5958:(e,r,t)=>{Promise.resolve().then(t.bind(t,4593)),Promise.resolve().then(t.bind(t,1279)),Promise.resolve().then(t.bind(t,9208))},6241:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(9384),a=t(2348);function n(...e){return(0,a.QP)((0,s.$)(e))}},6542:(e,r,t)=>{"use strict";t.d(r,{default:()=>n});var s=t(687);t(3210);var a=t(474);let n=()=>(0,s.jsxs)("div",{className:"flex flex-wrap mt-10 gap-2.5",children:[(0,s.jsx)("p",{className:"mr-10",children:"Or Continue with "}),(0,s.jsx)(a.default,{src:"/icons/facebook.svg",alt:"Facebook",width:20,height:20,className:"object-contain mr-2.5 "}),(0,s.jsx)(a.default,{src:"/icons/google.svg",alt:"Google",width:20,height:20,className:"object-contain mr-2.5"}),(0,s.jsx)(a.default,{src:"/icons/apple.svg",alt:"Apple",width:20,height:20,className:"object-contain mr-2.5 "})]})},6749:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},6814:(e,r,t)=>{"use strict";t.d(r,{Y9:()=>i,j2:()=>d});var s=t(9859),a=t(3560),n=t(6056);let{handlers:i,signIn:o,signOut:l,auth:d}=(0,s.Ay)({providers:[a.A,n.A],secret:process.env.AUTH_SECRET})},7470:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(7413),a=t(5896),n=t(3384);let i=({children:e})=>(0,s.jsx)("main",{className:"flex min-h-screen justify-center items-center bg-cover  px-4 py-10",children:(0,s.jsxs)("section",{className:" shadow-light100_dark100 py-10 px-4    border light-border rounded-[10px] min-w-full sm:min-w-[520px] sm:px-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,s.jsxs)("div",{className:"space-y-2.5",children:[(0,s.jsx)("h1",{className:"h2-bold text-dark100_light900",children:"Sign Up - For Applicants"}),(0,s.jsx)("p",{className:"paragraph-regular text-dark500_light400",children:"Fill out form"})]}),(0,s.jsx)(n.default,{src:"/images/site-logo.svg",alt:"Intview Logo",width:50,height:50,className:"object-contain"})]}),e,(0,s.jsx)(a.default,{})]})})},7539:(e,r,t)=>{Promise.resolve().then(t.bind(t,6542)),Promise.resolve().then(t.t.bind(t,6533,23))},7867:(e,r,t)=>{Promise.resolve().then(t.bind(t,5896)),Promise.resolve().then(t.t.bind(t,9603,23))},8014:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h,metadata:()=>u});var s=t(7413),a=t(363),n=t(6649),i=t.n(n),o=t(5843),l=t.n(o);t(2704);var d=t(25),c=t(2175),m=t(6814);let u={title:"Interview AI",description:`A community driven platform for asking and answering programming questions. Get help, share knowledge 
  and collaborate with developers from arount the world. Explore topics in web development, mobile app development, 
  data structures, and more.`,icons:{icon:"/images/site-logo.svg"}},h=async({children:e})=>{let r=await (0,m.j2)();return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsx)(c.SessionProvider,{session:r,children:(0,s.jsxs)("body",{className:`${i().className} ${l().variable} antialiased`,children:[(0,s.jsx)(d.default,{attribute:"class",defaultTheme:"light",children:e}),(0,s.jsx)(a.Toaster,{})]})})})}},9360:(e,r,t)=>{"use strict";t.d(r,{I:()=>a,m:()=>n});var s=t(7566);let a=s.Ik({email:s.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:s.Yj().min(6,{error:"Password must be atleast 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"})}),n=s.Ik({confirmPassword:s.Yj().min(6,{error:"Password must be atleast 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain atleast one uppercase character").regex(/[a-z]/,"Password must contain atleast one lowercase character").regex(/[0-9]/,"Password must contain atleast one number").regex(/[^a-zA-Z0-9]/,"Password must contain atleast one special character"),name:s.Yj().min(1,{message:"Name is required."}).max(50,{message:"Name cannot exceed 50 characters."}).regex(/^[a-zA-Z\s]+$/,{message:"Name can only contain letters and spaces."}),email:s.Yj().min(1,{error:"Email is required"}).email({error:"Please provide a valid email address."}),password:s.Yj().min(6,{error:"Password must be atleast 6 characters long"}).max(100,{error:"Password cannot exceed 100 characters"}).regex(/[A-Z]/,"Password must contain atleast one uppercase character").regex(/[a-z]/,"Password must contain atleast one lowercase character").regex(/[0-9]/,"Password must contain atleast one number").regex(/[^a-zA-Z0-9]/,"Password must contain atleast one special character")})}};