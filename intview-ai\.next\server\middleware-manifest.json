{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_5399b416._.js", "server/edge/chunks/node_modules_@auth_core_5ebafa38._.js", "server/edge/chunks/node_modules_jose_dist_webapi_49ff121e._.js", "server/edge/chunks/node_modules_e184ff1b._.js", "server/edge/chunks/[root-of-the-server]__df53d061._.js", "server/edge/chunks/edge-wrapper_3d09a47d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "VASB8nVds8jkLTtb8GENlU+fOP63zicRwAijtw+ZwlY=", "__NEXT_PREVIEW_MODE_ID": "47ea2c8fd94836baf634d35c024aa83a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "774d42c69b1c2fb09991324b62a584168d178c6cb1e4a08266240a787778b5a5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c8ce82dc2e76864efb6edda251a1285490fc0d785be7231c4a26c7e33f10cda5"}}}, "sortedMiddleware": ["/"], "functions": {}}