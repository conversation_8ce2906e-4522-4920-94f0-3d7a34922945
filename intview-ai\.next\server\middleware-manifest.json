{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "7db30VTGt1T9pbkwhyO86", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "VASB8nVds8jkLTtb8GENlU+fOP63zicRwAijtw+ZwlY=", "__NEXT_PREVIEW_MODE_ID": "cddb23ef0cfb9aacd7e9b8b735570260", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9221a6a8134d71500ac7d12175bd30b936d5f119a870ca19c85f8b0213a754fc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9d702a4167064c4cd6135f57d25ba56ff7a2548d5a9b3ca6a12556164b5622e4"}}}, "functions": {}, "sortedMiddleware": ["/"]}