[{"D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\layout.tsx": "1", "D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-in\\page.tsx": "2", "D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-up\\page.tsx": "3", "D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\interview\\page.tsx": "4", "D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\job-posts\\page.tsx": "5", "D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\layout.tsx": "6", "D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\page.tsx": "7", "D:\\Softwares\\Ai bot\\intview-ai\\app\\api\\auth\\[...nextauth]\\route.ts": "8", "D:\\Softwares\\Ai bot\\intview-ai\\app\\layout.tsx": "9", "D:\\Softwares\\Ai bot\\intview-ai\\components\\CandidateImage.tsx": "10", "D:\\Softwares\\Ai bot\\intview-ai\\components\\DIDAvatar.tsx": "11", "D:\\Softwares\\Ai bot\\intview-ai\\components\\forms\\AuthForm.tsx": "12", "D:\\Softwares\\Ai bot\\intview-ai\\components\\forms\\SocialAuthForm.tsx": "13", "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\Analysis.tsx": "14", "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\FinishInterview.tsx": "15", "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\InterviewInstructions.tsx": "16", "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\InterviewRecording.tsx": "17", "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\InterviewWithDID.tsx": "18", "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\QuestionsPage.tsx": "19", "D:\\Softwares\\Ai bot\\intview-ai\\components\\InterviewCard.tsx": "20", "D:\\Softwares\\Ai bot\\intview-ai\\components\\InterviewLayout.tsx": "21", "D:\\Softwares\\Ai bot\\intview-ai\\components\\InterviewPreparationOverlay.tsx": "22", "D:\\Softwares\\Ai bot\\intview-ai\\components\\JobInfoCard.tsx": "23", "D:\\Softwares\\Ai bot\\intview-ai\\components\\navigation\\navbar\\index.tsx": "24", "D:\\Softwares\\Ai bot\\intview-ai\\components\\navigation\\navbar\\Theme.tsx": "25", "D:\\Softwares\\Ai bot\\intview-ai\\components\\QuestionsList.tsx": "26", "D:\\Softwares\\Ai bot\\intview-ai\\components\\sideBar.tsx": "27", "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\button.tsx": "28", "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\dropdown-menu.tsx": "29", "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\form.tsx": "30", "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\input.tsx": "31", "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\label.tsx": "32", "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\sonner.tsx": "33", "D:\\Softwares\\Ai bot\\intview-ai\\components\\VideoTranscript.tsx": "34", "D:\\Softwares\\Ai bot\\intview-ai\\lib\\utils.ts": "35", "D:\\Softwares\\Ai bot\\intview-ai\\lib\\validations.ts": "36", "D:\\Softwares\\Ai bot\\intview-ai\\lib\\videoPreloadingService.ts": "37"}, {"size": 1159, "mtime": 1754375401205, "results": "38", "hashOfConfig": "39"}, {"size": 419, "mtime": 1754375401206, "results": "40", "hashOfConfig": "39"}, {"size": 450, "mtime": 1754375401207, "results": "41", "hashOfConfig": "39"}, {"size": 1499, "mtime": 1754571268288, "results": "42", "hashOfConfig": "39"}, {"size": 267, "mtime": 1754549776824, "results": "43", "hashOfConfig": "39"}, {"size": 995, "mtime": 1754540332263, "results": "44", "hashOfConfig": "39"}, {"size": 1258, "mtime": 1754447219846, "results": "45", "hashOfConfig": "39"}, {"size": 120, "mtime": 1754375401212, "results": "46", "hashOfConfig": "39"}, {"size": 1694, "mtime": 1754550294826, "results": "47", "hashOfConfig": "39"}, {"size": 500, "mtime": 1754570120695, "results": "48", "hashOfConfig": "39"}, {"size": 8584, "mtime": 1754581545581, "results": "49", "hashOfConfig": "39"}, {"size": 3496, "mtime": 1754447289402, "results": "50", "hashOfConfig": "39"}, {"size": 1851, "mtime": 1754447287547, "results": "51", "hashOfConfig": "39"}, {"size": 858, "mtime": 1754561256062, "results": "52", "hashOfConfig": "39"}, {"size": 1375, "mtime": 1754550681506, "results": "53", "hashOfConfig": "39"}, {"size": 6632, "mtime": 1754570620448, "results": "54", "hashOfConfig": "39"}, {"size": 1429, "mtime": 1754549109541, "results": "55", "hashOfConfig": "39"}, {"size": 7248, "mtime": 1754581268176, "results": "56", "hashOfConfig": "39"}, {"size": 1312, "mtime": 1754549099709, "results": "57", "hashOfConfig": "39"}, {"size": 1285, "mtime": 1754558202840, "results": "58", "hashOfConfig": "39"}, {"size": 266, "mtime": 1754488943054, "results": "59", "hashOfConfig": "39"}, {"size": 1177, "mtime": 1754576955444, "results": "60", "hashOfConfig": "39"}, {"size": 1514, "mtime": 1754544355206, "results": "61", "hashOfConfig": "39"}, {"size": 5341, "mtime": 1754553434160, "results": "62", "hashOfConfig": "39"}, {"size": 1428, "mtime": 1754383178039, "results": "63", "hashOfConfig": "39"}, {"size": 1912, "mtime": 1754570120697, "results": "64", "hashOfConfig": "39"}, {"size": 5218, "mtime": 1754557296896, "results": "65", "hashOfConfig": "39"}, {"size": 2182, "mtime": 1754375401259, "results": "66", "hashOfConfig": "39"}, {"size": 8541, "mtime": 1754375401261, "results": "67", "hashOfConfig": "39"}, {"size": 3926, "mtime": 1754375401261, "results": "68", "hashOfConfig": "39"}, {"size": 988, "mtime": 1754375401263, "results": "69", "hashOfConfig": "39"}, {"size": 635, "mtime": 1754375401263, "results": "70", "hashOfConfig": "39"}, {"size": 589, "mtime": 1754375401264, "results": "71", "hashOfConfig": "39"}, {"size": 944, "mtime": 1754539526714, "results": "72", "hashOfConfig": "39"}, {"size": 172, "mtime": 1754375401272, "results": "73", "hashOfConfig": "39"}, {"size": 1799, "mtime": 1754375401274, "results": "74", "hashOfConfig": "39"}, {"size": 10319, "mtime": 1754573754544, "results": "75", "hashOfConfig": "39"}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "183vquo", {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\layout.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-in\\page.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-up\\page.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\interview\\page.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\job-posts\\page.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\layout.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\app\\(root)\\page.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\app\\layout.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\CandidateImage.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\DIDAvatar.tsx", ["187", "188", "189", "190", "191"], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\forms\\AuthForm.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\forms\\SocialAuthForm.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\Analysis.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\FinishInterview.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\InterviewInstructions.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\InterviewRecording.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\InterviewWithDID.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\interview\\QuestionsPage.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\InterviewCard.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\InterviewLayout.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\InterviewPreparationOverlay.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\JobInfoCard.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\navigation\\navbar\\index.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\navigation\\navbar\\Theme.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\QuestionsList.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\sideBar.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\button.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\dropdown-menu.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\form.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\input.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\label.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\ui\\sonner.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\components\\VideoTranscript.tsx", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\lib\\utils.ts", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\lib\\validations.ts", [], [], "D:\\Softwares\\Ai bot\\intview-ai\\lib\\videoPreloadingService.ts", [], [], {"ruleId": "192", "severity": 2, "message": "193", "line": 34, "column": 10, "nodeType": "194", "messageId": "195", "endLine": 34, "endColumn": 26, "suggestions": "196"}, {"ruleId": "197", "severity": 2, "message": "193", "line": 34, "column": 10, "nodeType": null, "messageId": "195", "endLine": 34, "endColumn": 26}, {"ruleId": "198", "severity": 2, "message": "199", "line": 107, "column": 32, "nodeType": "194", "messageId": "200", "endLine": 107, "endColumn": 35}, {"ruleId": "201", "severity": 2, "message": "202", "line": 119, "column": 19, "nodeType": "203", "messageId": "204", "endLine": 119, "endColumn": 22, "suggestions": "205"}, {"ruleId": "206", "severity": 1, "message": "207", "line": 154, "column": 6, "nodeType": "208", "endLine": 154, "endColumn": 34, "suggestions": "209"}, "no-unused-vars", "'preloadingStatus' is assigned a value but never used.", "Identifier", "unusedVar", ["210"], "@typescript-eslint/no-unused-vars", "promise/param-names", "Promise constructor parameters must be named to match \"^_?resolve$\"", "resolveParamNames", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["211", "212"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'createTalk'. Either include it or remove the dependency array.", "ArrayExpression", ["213"], {"messageId": "214", "data": "215", "fix": "216", "desc": "217"}, {"messageId": "218", "fix": "219", "desc": "220"}, {"messageId": "221", "fix": "222", "desc": "223"}, {"desc": "224", "fix": "225"}, "removeVar", {"varName": "226"}, {"range": "227", "text": "228"}, "Remove unused variable 'preloadingStatus'.", "suggestUnknown", {"range": "229", "text": "230"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "231", "text": "232"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [text, preGeneratedVideoUrl, createTalk]", {"range": "233", "text": "234"}, "preloadingStatus", [1032, 1048], "", [3699, 3702], "unknown", [3699, 3702], "never", [4729, 4757], "[text, preGeneratedVideoUrl, createTalk]"]