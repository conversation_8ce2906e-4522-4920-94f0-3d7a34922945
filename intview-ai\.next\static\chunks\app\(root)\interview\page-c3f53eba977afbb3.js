(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[812],{3999:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var a=s(2596),r=s(9688);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},4209:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>_});var a=s(5155),r=s(2115),i=s(7168),l=s(2138);let n=["The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.","The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned."],o=["To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.","To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face."],c=["Environment Requirements Ensure you are in a quiet, distraction-free space. Sit in a well-lit area so the avatar can see you clearly. Use a stable internet connection and a working camera & microphone .","AI Interview Format Your interviewer will be an AI avatar, speaking and listening in a natural, conversational style. You will respond to 5 preset questions, with roughly under 10 minutes total interview time. You may be gently prompted if your answers run long—please stay within the time suggested .","Recording & Usage This session will be fully recorded (audio & video) for review by our hiring team. Your responses and the recording will be processed by our AI scoring system to evaluate communication, problem-solving, and fit. All data is stored securely and used only for the purposes of hiring this role .","Independence & Integrity Please answer without external aids (notes, websites, or other people). If background noise or interruptions occur, you may be prompted to pause and restart your answer ."],d=e=>{let{candidateName:t="Jonathan",jobTitle:s="Insurance Agent",languages:d=["English","Chinese"],instructions:u=n,environmentChecklist:m=o,disclaimers:h=c,onNext:x}=e,[p,g]=(0,r.useState)(!1);return(0,a.jsx)("div",{className:"flex-1 border border-gray-400 rounded-md h-fit bg-white",children:(0,a.jsxs)("div",{className:"p-4 flex flex-col text-[#38383a]",children:[(0,a.jsx)("p",{className:"font-semibold mb-8 text-xl",children:"Instructions for Interview!"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:" mb-2 text-md",children:["Hello ",t,"!"]}),(0,a.jsxs)("p",{className:"text-sm mb-4",children:["As part of the process you are required to complete an AI video assessment for the role of the ",s,"."]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Interview Language"}),(0,a.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:d.map((e,t)=>(0,a.jsx)("li",{children:e},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Instructions"}),(0,a.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:u.map((e,t)=>(0,a.jsx)("li",{children:e},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Environment Checklist:"}),(0,a.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:m.map((e,t)=>(0,a.jsx)("li",{children:e},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold mb-2 text-lg",children:"Important Disclaimers:"}),(0,a.jsx)("ul",{className:"list-disc list-inside space-y-2 text-sm",children:h.map((e,t)=>(0,a.jsx)("li",{children:e},t))})]}),(0,a.jsxs)("div",{className:"flex items-start gap-2 mt-6",children:[(0,a.jsx)("input",{type:"checkbox",id:"terms",checked:p,onChange:e=>g(e.target.checked),className:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),(0,a.jsxs)("label",{htmlFor:"terms",className:"text-[11px] text-[#38383a]",children:["By checking this box, you agree with AI Interview"," ",(0,a.jsx)("span",{className:"text-primary cursor-pointer font-medium",children:"Terms of use"}),"."]})]}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)(i.$,{disabled:!p,variant:"default",size:"lg",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>x&&x(),children:["Proceed",(0,a.jsx)(l.A,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})})]})]})})};var u=s(4516),m=s(6325);let h=()=>(0,a.jsx)("div",{className:"bg-white p-4 rounded-2xl shadow-sm mb-6 max-w-xl",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-3",children:"UX/UI Designer for Ai-Interview Web App"}),(0,a.jsxs)("div",{className:"flex gap-2 leading-relaxed mb-3 flex-wrap",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600 font-medium",children:["$500 - $1000 ",(0,a.jsx)("span",{className:"font-extrabold px-1",children:"\xb7"})]}),(0,a.jsxs)("div",{className:"flex gap-1 items-center",children:[(0,a.jsx)(u.A,{className:"w-4 h-5"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 font-medium",children:"New York"})]}),(0,a.jsxs)("div",{className:"flex gap-1 items-center",children:[(0,a.jsx)(m.A,{className:"w-4 h-5"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 font-medium",children:"Onsite / Remote"})]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"We're building an AI-powered interview tool. We expect you to help users prepare by giving human interview experience generation."})]}),(0,a.jsx)("span",{className:"text-xs bg-[#CCFFB1] text-green-700 px-3 py-1 rounded-full font-medium",children:"Active"})]})}),x=e=>{let{currentQuestion:t=1,className:s}=e,r=["Tell us about yourself?","What are your strengths?","Why do you want this job?","Where do you see yourself in 5 years?"];return(0,a.jsxs)("div",{className:"rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] h-[488px] shadow-sm overflow-y-auto scrollbar-hidden ".concat(s||""),children:[" ",(0,a.jsx)("h3",{className:"font-semibold text-lg mb-6",children:"Questions"}),(0,a.jsx)("ul",{className:"relative space-y-8  ",children:Array.from({length:4},(e,s)=>(0,a.jsxs)("li",{className:"relative flex items-start space-x-3 mt-4 mb-0 sm:mb-5",children:[3!==s&&(0,a.jsx)("span",{className:"absolute left-[17px] pl-[3px] top-6 mt-11 h-10 w-[3px] rounded-full bg-gradient-to-b from-white to-[#6938EF]"}),(0,a.jsx)("div",{className:"rounded-full w-7 h-7 mt-7 flex items-center p-5 justify-center text-sm font-medium z-10 ".concat(s+1===t?"bg-[#6938EF] text-white":s+1<t?"bg-green-500 text-white":"bg-[#C7ACF5] text-white"),children:s+1<t?"✓":s+1}),(0,a.jsx)("span",{className:"text-md font-medium mt-7 ".concat(s+1===t?"text-[#6938EF] font-semibold":"text-[#616161]"),children:r[s]})]},s))})]})};var p=s(6766);let g={src:"/_next/static/media/avator.608a8af8.png",height:663,width:363,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAICAMAAADp7a43AAAAP1BMVEVobHRET2VocYCsrJ2MlKd7cmNgVVPJ1eeCgXHe5/MYJD6jn613eHJZWmaUnX3Cxc/NxL+PlHhIWnzW3eh8enipbN0SAAAACHRSTlP8/////////goJZaEAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAsSURBVHicBcEHAgAQDACxQ9Ha6/9vlaAaBjHqpqUXOOI8RXzHyVykahfM8gcVIAEOI071+QAAAABJRU5ErkJggg==",blurWidth:4,blurHeight:8},f=e=>{let{className:t}=e;return(0,a.jsx)("div",{className:"mt-6 md:mt-0",children:(0,a.jsx)(p.default,{src:g,alt:"Interviewee",className:"rounded-lg object-cover ".concat(t),width:300,height:300})})},v=e=>{let{children:t}=e;return(0,a.jsx)("div",{className:"border rounded-lg p-6 min-h-[600px] mb-4 flex-1",children:t})},w=e=>{let{onNext:t}=e;return(0,a.jsxs)("div",{className:"h-screen",children:[(0,a.jsx)(h,{}),(0,a.jsxs)(v,{children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start",children:[(0,a.jsx)(x,{className:"h-[550px]"}),(0,a.jsx)(f,{})]}),(0,a.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:(0,a.jsxs)(i.$,{variant:"default",size:"lg",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>t&&t(),children:["Start Interview",(0,a.jsx)(l.A,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})})]})]})};var b=s(646),j=s(5657),y=s(2900);let N="https://api.d-id.com/talks",A=e=>{let{text:t,videoUrl:s,onVideoReady:i,onVideoEnd:l,className:n="",preloadingService:o,showPreloadingStatus:c=!1,priority:d=0}=e,[u,m]=(0,r.useState)(s||null),[h,x]=(0,r.useState)(!1),[p,g]=(0,r.useState)(null),[f,v]=(0,r.useState)(null),w=(0,r.useRef)(null),b=async e=>{if(e.trim()){x(!0),m(null),g(null);try{let t=null;if(o)try{t=await o.getVideoUrl(e,d)}catch(e){console.warn("Failed to get video from preloading service, falling back to direct API:",e)}if(!t){let s={script:{type:"text",input:e.trim()}},a=await fetch(N,{method:"POST",headers:{Authorization:"Basic ".concat(btoa("******************************:iA53PWR1twRC9ciLUfrS_")),"Content-Type":"application/json"},body:JSON.stringify(s)});if(!a.ok)throw Error("Failed to create talk: ".concat(a.statusText));let r=(await a.json()).id,i=0;for(;!t&&i<30;){let e=await fetch("".concat(N,"/").concat(r),{headers:{Authorization:"Basic ".concat(btoa("******************************:iA53PWR1twRC9ciLUfrS_")),"Content-Type":"application/json"}}),s=await e.json();if("done"===s.status&&s.result_url)t=s.result_url;else if("error"===s.status)throw Error("Video generation failed");else await new Promise(e=>setTimeout(e,3e3)),i++}if(!t)throw Error("Video generation timed out")}m(t),null==i||i()}catch(e){console.error("D-ID API Error:",e),g(e.message||"Failed to generate video")}finally{x(!1)}}};return(0,r.useEffect)(()=>{if(o&&c){let e=setInterval(()=>{v(o.getQueueStatus())},1e3);return()=>clearInterval(e)}},[o,c]),(0,r.useEffect)(()=>{s&&(m(s),x(!1),g(null))},[s]),(0,r.useEffect)(()=>{t&&t.trim()&&!s&&b(t)},[t,s]),(0,r.useEffect)(()=>{let e=w.current;if(e){let t=()=>{null==l||l()};return e.addEventListener("ended",t),()=>{e.removeEventListener("ended",t)}}},[u,l]),(0,a.jsxs)("div",{className:"relative w-full h-full ".concat(n),children:[(0,a.jsx)("div",{className:"w-full h-full relative",children:!u||h||p?(0,a.jsxs)(y.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.3},className:"w-full h-full bg-gradient-to-br from-blue-400 via-indigo-500 to-purple-600 flex flex-col items-center justify-center relative rounded-lg",children:[(0,a.jsx)(j.A,{className:"w-16 h-16 sm:w-24 sm:h-24 lg:w-32 lg:h-32 text-white/90 mb-4 lg:mb-8"}),(0,a.jsxs)("div",{className:"text-center text-white px-4",children:[(0,a.jsx)("h3",{className:"text-lg sm:text-xl lg:text-2xl font-bold mb-1 lg:mb-2",children:"AI Interviewer"}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 lg:px-4 lg:py-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 lg:w-3 lg:h-3 bg-green-400 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-white font-medium text-sm lg:text-base",children:"Ready"})]})]}),(0,a.jsx)("div",{className:"absolute top-4 left-4 lg:top-8 lg:left-8 w-8 h-8 lg:w-16 lg:h-16 bg-white/10 rounded-full blur-xl"}),(0,a.jsx)("div",{className:"absolute bottom-6 right-6 lg:bottom-12 lg:right-12 w-12 h-12 lg:w-24 lg:h-24 bg-white/5 rounded-full blur-2xl"}),(0,a.jsx)("div",{className:"absolute top-1/3 right-4 lg:right-8 w-4 h-4 lg:w-8 lg:h-8 bg-white/20 rounded-full blur-sm"})]},"placeholder"):(0,a.jsxs)(y.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.3},className:"w-full h-full relative",children:[(0,a.jsx)("video",{ref:w,src:u,autoPlay:!0,className:"w-full h-full object-cover rounded-lg",onLoadedData:()=>null==i?void 0:i()},u),(0,a.jsxs)(y.P.div,{initial:{scale:0},animate:{scale:1},className:"absolute top-4 right-4 flex items-center space-x-2 bg-black/20 backdrop-blur-sm rounded-full px-3 py-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-white text-sm font-medium",children:"Speaking"})]})]},u)}),p&&(0,a.jsx)(y.P.div,{initial:{opacity:0},animate:{opacity:1},className:"absolute inset-0 bg-red-50 flex flex-col items-center justify-center rounded-lg border-2 border-red-200",children:(0,a.jsxs)("div",{className:"text-center text-red-600 px-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Error"}),(0,a.jsx)("p",{className:"text-sm",children:p})]})})]})},k="https://api.d-id.com/talks";class C extends Error{constructor(e,t){super(e),this.statusCode=t,this.name="DIDApiError"}}class E{generateCacheKey(e){let t=0;for(let s=0;s<e.length;s++)t=(t<<5)-t+e.charCodeAt(s),t&=t;return"did_video_".concat(Math.abs(t))}loadCacheFromStorage(){try{let e=localStorage.getItem("did_video_cache");e&&(this.cache=JSON.parse(e))}catch(e){console.warn("Failed to load video cache from storage:",e),this.cache={}}}saveCacheToStorage(){try{localStorage.setItem("did_video_cache",JSON.stringify(this.cache))}catch(e){console.warn("Failed to save video cache to storage:",e)}}cleanExpiredCache(){let e=Date.now(),t=!1;for(let s in this.cache)this.cache[s].expiresAt<e&&(delete this.cache[s],t=!0);t&&this.saveCacheToStorage()}async generateVideo(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,s={script:{type:"text",input:e.trim()},...this.config.sourceUrl&&{source_url:this.config.sourceUrl}};try{var a,r,i;let e=await fetch(k,{method:"POST",headers:{Authorization:"Basic ".concat(btoa(this.config.apiKey)),"Content-Type":"application/json"},body:JSON.stringify(s),signal:null==(a=this.abortController)?void 0:a.signal});if(!e.ok)throw new C("Failed to create talk: ".concat(e.statusText),e.status);let t=(await e.json()).id,l=0;for(;l<30;){if(null==(r=this.abortController)?void 0:r.signal.aborted)throw new C("Request aborted");let e=await fetch("".concat(k,"/").concat(t),{headers:{Authorization:"Basic ".concat(btoa(this.config.apiKey)),"Content-Type":"application/json"},signal:null==(i=this.abortController)?void 0:i.signal}),s=await e.json();if("done"===s.status&&s.result_url)return s.result_url;if("error"===s.status)throw new C("Video generation failed");await new Promise(e=>setTimeout(e,3e3)),l++}throw new C("Video generation timed out")}catch(s){if(t<this.config.retryAttempts&&!(s instanceof C&&401===s.statusCode))return console.warn("Retrying video generation (attempt ".concat(t+1,"):"),s),await new Promise(e=>setTimeout(e,1e3*(t+1))),this.generateVideo(e,t+1);throw console.error("D-ID API Error:",s),s instanceof C?s:new C(s instanceof Error?s.message:"Unknown error generating video")}}async processQueue(){for(;this.preloadQueue.length>0&&this.activeRequests<this.config.maxConcurrentRequests;){let e=this.preloadQueue.findIndex(e=>"pending"===e.status);if(-1===e)break;let t=this.preloadQueue[e];t.status="loading",this.activeRequests++;try{let s=await this.generateVideo(t.text),a=this.generateCacheKey(t.text),r=Date.now();this.cache[a]={url:s,timestamp:r,expiresAt:r+this.config.cacheExpiration},this.saveCacheToStorage(),t.status="completed",this.preloadQueue.splice(e,1)}catch(s){t.status="failed",t.error=s instanceof Error?s.message:"Unknown error",this.preloadQueue.splice(e,1)}finally{this.activeRequests--}}}async getVideoUrl(e){var t;let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(!e.trim())throw new C("Question text is required");let a=this.generateCacheKey(e),r=this.cache[a];if(r&&r.expiresAt>Date.now())return r.url;let i=this.preloadQueue.find(t=>t.text===e);if(i){if(i.promise)return i.promise;s>i.priority&&(i.priority=s,this.preloadQueue.sort((e,t)=>t.priority-e.priority))}else{let t={id:"task_".concat(Date.now(),"_").concat(Math.random()),text:e,priority:s,status:"pending"};t.promise=new Promise((e,s)=>{let r=()=>{let i=this.preloadQueue.find(e=>e.id===t.id);if(i)"failed"===i.status?s(Error(i.error||"Video generation failed")):setTimeout(r,500);else{let t=this.cache[a];t?e(t.url):s(Error("Task completed but no cached result found"))}};setTimeout(r,100)}),this.preloadQueue.push(t),this.preloadQueue.sort((e,t)=>t.priority-e.priority)}return this.processQueue(),(null==i?void 0:i.promise)||(null==(t=this.preloadQueue.find(t=>t.text===e))?void 0:t.promise)||Promise.reject(Error("Failed to create task"))}preloadVideos(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;e.forEach((e,s)=>{this.getVideoUrl(e,t-s).catch(t=>{console.warn('Failed to preload video for: "'.concat(e.substring(0,50),'..."'),t)})})}getQueueStatus(){let e=this.preloadQueue.filter(e=>"pending"===e.status).length,t=this.preloadQueue.filter(e=>"loading"===e.status).length;return{pending:e,loading:t,completed:this.preloadQueue.filter(e=>"completed"===e.status).length,failed:this.preloadQueue.filter(e=>"failed"===e.status).length,total:this.preloadQueue.length}}clearCache(){this.cache={},this.saveCacheToStorage()}abort(){this.abortController&&this.abortController.abort(),this.abortController=new AbortController,this.preloadQueue=[],this.activeRequests=0}destroy(){this.abort(),this.clearCache()}constructor(e){var t;this.cache={},this.preloadQueue=[],this.activeRequests=0,this.abortController=null,this.config={...e,cacheExpiration:e.cacheExpiration||36e5,maxConcurrentRequests:e.maxConcurrentRequests||3,retryAttempts:e.retryAttempts||2,sourceUrl:null!=(t=e.sourceUrl)?t:""},this.loadCacheFromStorage(),this.cleanExpiredCache()}}let I=e=>{let{questions:t,candidateName:s="Candidate",jobTitle:a="Position",apiKey:i,sourceUrl:l,autoStart:n=!1}=e,[o,c]=(0,r.useState)({isPreparingInterview:!1,progress:0,currentStep:"",totalQuestions:t.length,loadedQuestions:0,failedQuestions:0,estimatedTimeRemaining:0,error:null}),[d,u]=(0,r.useState)(0),[m,h]=(0,r.useState)(""),[x,p]=(0,r.useState)(!1),g=(0,r.useRef)(null),f=(0,r.useRef)(null),v=(0,r.useRef)(0),w=t.map((e,t)=>0===t?e.replace(/\{candidateName\}/g,s).replace(/\{jobTitle\}/g,a):e),b=(0,r.useCallback)(e=>{c(t=>({...t,...e}))},[]),j=(0,r.useCallback)((e,t,s)=>0===e?0:Math.ceil((Date.now()-s)/e*(t-e)/1e3),[]),y=(0,r.useCallback)(async()=>{if(!i)return void b({error:"API key is required for video generation",isPreparingInterview:!1});b({isPreparingInterview:!0,progress:0,currentStep:"Initializing interview preparation...",error:null}),v.current=Date.now();try{g.current=new E({apiKey:i,sourceUrl:l,cacheExpiration:36e5,maxConcurrentRequests:3,retryAttempts:2}),b({currentStep:"Starting background video generation..."}),g.current.preloadVideos(w,100),f.current=setInterval(()=>{if(g.current){let e=g.current.getQueueStatus(),t=e.total>0?e.completed/e.total*100:0,s=j(e.completed,e.total,v.current);b({progress:t,loadedQuestions:e.completed,failedQuestions:e.failed,estimatedTimeRemaining:s,currentStep:e.loading>0?"Generating video ".concat(e.completed+1," of ").concat(e.total,"..."):e.completed===e.total?"All questions ready!":"Preparing questions..."}),e.completed+e.failed>=e.total&&(f.current&&(clearInterval(f.current),f.current=null),N(0),b({isPreparingInterview:!1,currentStep:"Interview ready to start!"}),p(!0))}},500)}catch(e){console.error("Failed to start interview preparation:",e),b({error:e instanceof Error?e.message:"Failed to prepare interview",isPreparingInterview:!1})}},[i,l,w,b,j]),N=(0,r.useCallback)(async e=>{if(!g.current||e>=w.length)return;let t=w[e];try{let s=await g.current.getVideoUrl(t,1e3-e);h(s),u(e)}catch(t){console.error("Failed to load question ".concat(e,":"),t),h("")}},[w]),A=(0,r.useCallback)(async()=>{let e=d+1;return e<w.length&&(await N(e),!0)},[d,w.length,N]),k=(0,r.useCallback)(()=>w[d]||"",[w,d]),C=(0,r.useCallback)(()=>w.length>0?(d+1)/w.length*100:0,[w.length,d]);return(0,r.useEffect)(()=>{!n||o.isPreparingInterview||x||y()},[n,o.isPreparingInterview,x,y]),(0,r.useEffect)(()=>()=>{f.current&&clearInterval(f.current),g.current&&g.current.destroy()},[]),{...o,isInterviewReady:x,currentQuestionIndex:d,currentVideoUrl:m,currentQuestionText:k(),interviewProgress:C(),startPreparation:y,nextQuestion:A,loadQuestion:N,preloadingService:g.current,personalizedQuestions:w,isLastQuestion:d>=w.length-1}},S=e=>{let{isVisible:t}=e;return t?(0,a.jsx)(y.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50",children:(0,a.jsx)(y.P.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-white/95 backdrop-blur-sm rounded-2xl p-8 text-center shadow-2xl mx-4 max-w-md w-full",children:(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:"Preparing Interview"})})}):null},Q=e=>{let{onNext:t,candidateName:s="Jonathan",jobTitle:n="Insurance Agent"}=e,[o,c]=(0,r.useState)(!1),[d,u]=(0,r.useState)(!1),m=["Hello {candidateName}, welcome to your interview for the {jobTitle} position. Let's start with our first question: Tell us about yourself and your background."],p=I({questions:m,candidateName:s,jobTitle:n,apiKey:"******************************:iA53PWR1twRC9ciLUfrS_",autoStart:!1}),g=async()=>{c(!0),await p.startPreparation()},f=async()=>{p.isLastQuestion?null==t||t():(u(!1),await p.nextQuestion()||null==t||t())},w=p.isLastQuestion&&p.currentQuestionIndex>=m.length;return o?w?(0,a.jsxs)("div",{className:"h-screen",children:[(0,a.jsx)(h,{}),(0,a.jsx)(v,{children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center h-full",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)(b.A,{className:"w-16 h-16 text-green-500 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"Interview Completed!"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Thank you for completing the interview. Your responses have been recorded."})]}),(0,a.jsxs)(i.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>null==t?void 0:t(),children:["View Results",(0,a.jsx)(l.A,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})]})})]}):(0,a.jsxs)("div",{className:"h-screen",children:[(0,a.jsx)(h,{}),(0,a.jsxs)(v,{children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-10 justify-center items-center md:items-start",children:[(0,a.jsx)(x,{className:"h-[550px]",currentQuestion:p.currentQuestionIndex+1}),(0,a.jsx)("div",{className:"mt-6 md:mt-0",children:(0,a.jsx)(A,{className:"w-[300px] h-[300px]",text:p.currentQuestionText,videoUrl:p.currentVideoUrl,onVideoReady:()=>{setTimeout(()=>{u(!0)},2e3)},onVideoEnd:()=>{d||u(!0)}})})]}),(0,a.jsx)(S,{isVisible:p.isPreparingInterview,progress:p.progress,currentStep:p.currentStep,totalQuestions:p.totalQuestions,loadedQuestions:p.loadedQuestions,failedQuestions:p.failedQuestions,estimatedTimeRemaining:p.estimatedTimeRemaining}),(0,a.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:d?(0,a.jsxs)(i.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:f,children:[p.isLastQuestion?"Finish Interview":"Next Question",(0,a.jsx)(l.A,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]}):(0,a.jsx)("div",{className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center justify-center gap-2 bg-gray-200 text-gray-500",children:"Listen to the question"})})]})]}):(0,a.jsxs)("div",{className:"h-screen",children:[(0,a.jsx)(h,{}),(0,a.jsxs)(v,{children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-10 justify-center items-center md:items-start",children:[(0,a.jsx)(x,{className:"h-[550px]",currentQuestion:0}),(0,a.jsx)("div",{className:"mt-6 md:mt-0",children:(0,a.jsx)(A,{className:"w-[300px] h-[300px]"})})]}),(0,a.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:(0,a.jsxs)(i.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:g,children:["Start Interview",(0,a.jsx)(l.A,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})}),(0,a.jsx)("div",{className:"flex justify-center mt-5 text-2xl font-semibold text-primary",children:"Ready to begin"})]})]})},R=()=>(0,a.jsxs)("div",{className:"rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] shadow-sm h-[488px] overflow-y-auto scrollbar-hidden",children:[(0,a.jsx)("p",{className:"text-lg font-semibold text-black mb-5",children:"Video Transcript"}),(0,a.jsx)("p",{children:"Tell us about yourselves?"}),(0,a.jsx)("p",{className:"text-sm mt-4 leading-7 ",children:"Motivated and results-driven professional with a proven track record of success in dynamic work environments. Known for strong problem-solving skills, a collaborative mindset, and a dedication to continuous learning and improvement. Brings a blend of technical expertise, strategic thinking, and effective communication to contribute meaningfully to team and organizational goals. Eager to take on new challenges and deliver impactful outcomes in a fast-paced role."})]}),T=e=>{let{onNext:t}=e;return(0,a.jsxs)("div",{className:"h-screen",children:[(0,a.jsx)(h,{}),(0,a.jsxs)(v,{children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start",children:[(0,a.jsx)(x,{}),(0,a.jsx)(f,{className:"w-[265px]"}),(0,a.jsx)(R,{})]}),(0,a.jsx)("div",{className:"flex justify-center mt-10 gap-4",children:(0,a.jsxs)(i.$,{variant:"default",className:"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white",onClick:()=>t&&t(),children:["Finish Interview",(0,a.jsx)(l.A,{className:"w-6 h-6 duration-300 group-hover:translate-x-1"})]})})]})]})},P={src:"/_next/static/media/trophy.73528452.png",height:28,width:28,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAANlBMVEXNszH7qED//1HPmRGtnSfDoyrn1T1MaXHr1j+cqzrVpR7SoiDosSnluR7esSLrwyPptBvv0S75zPcvAAAAEnRSTlMR/hacHCkqADMIjM+nl9x4XaVFuRFRAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAPElEQVR4nBXFSRIAIQgAsUZBwN3/f3ZqcglJMSskhKpq/Pe9uwZWn8irRrtLZN0GkedkgLecM5vjzhi4fzkhAbtZdsbsAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},F=()=>(0,a.jsxs)("div",{className:"flex  justify-between bg-white rounded-2xl shadow-md p-4 w-full max-w-xl mb-5",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"bg-[#F4F1FE] rounded-xl px-4 py-4 text-center w-30",children:[(0,a.jsx)("div",{className:"flex justify-center mb-2",children:(0,a.jsx)(p.default,{src:P,alt:"Trophy"})}),(0,a.jsx)("p",{className:"text-xl font-bold text-[#1E1E1E]",children:"55%"}),(0,a.jsx)("p",{className:"text-xs text-gray-600 mt-1",children:"Overall Score"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-sm sm:text-[6px] md:text-base lg:text-lg text-[#1E1E1E] mb-2",children:"AI Interviewer"}),(0,a.jsx)("p",{className:"text-sm text-gray-800 font-medium",children:"UI UX Designer"}),(0,a.jsx)("p",{className:"text-sm text-gray-800 font-medium",children:"18th June, 2025"})]})]}),(0,a.jsx)("div",{className:"top-0",children:(0,a.jsx)("span",{className:"bg-[#CCFFB1] text-[#1E1E1E] text-xs px-4 py-1 rounded-full",children:"Evaluated"})})]}),V=e=>{let{label:t,value:s,color:r="bg-orange-500"}=e;return(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm mb-1",children:[(0,a.jsx)("span",{className:"mb-1",children:t}),(0,a.jsxs)("span",{children:[s,"/100"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:(0,a.jsx)("div",{className:"h-2.5 rounded-full ".concat(r),style:{width:"".concat(s,"%")}})})]})};var q=s(6636);s(839);let U=e=>{let{label:t,percent:s,color:r,trailColor:i}=e;return(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-1 mb-2",children:[(0,a.jsx)("p",{className:"text-sm font-semibold mb-3",children:t}),(0,a.jsx)("div",{className:"w-32 h-28",children:(0,a.jsx)(q.QF,{value:s,text:"".concat(s,"%"),strokeWidth:10,styles:(0,q.Hf)({textSize:"12px",pathColor:r,textColor:"#5a5a5a",trailColor:i})})})]})},D=()=>(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 border p-6 rounded-xl w-full max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between font-semibold mb-4",children:[(0,a.jsx)("span",{children:"Resume Score"}),(0,a.jsx)("span",{children:"65%"})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsx)(V,{label:"Company Fit",value:66}),(0,a.jsx)(V,{label:"Relevant Experience",value:66,color:"bg-purple-600"}),(0,a.jsx)(V,{label:"Job Knowledge",value:66}),(0,a.jsx)(V,{label:"Education",value:66}),(0,a.jsx)(V,{label:"Hard Skills",value:66})]}),(0,a.jsxs)("div",{className:"mt-4 font-medium flex justify-between bg-gray-100 text-sm text-center border rounded-xl p-8",children:["Over All Score \xa0 ",(0,a.jsx)("span",{className:"text-black",children:"66/100"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 shadow-sm",children:[(0,a.jsx)("div",{className:"font-semibold mb-4",children:"Video Score"}),(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsx)(V,{label:"Professionalism",value:64}),(0,a.jsx)(V,{label:"Energy Level",value:56,color:"bg-purple-600"}),(0,a.jsx)(V,{label:"Communication",value:58}),(0,a.jsx)(V,{label:"Sociability",value:70})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 flex flex-col space-y-2   gap-5 shadow-sm",children:[(0,a.jsx)("p",{className:"font-semibold",children:"AI Rating"}),(0,a.jsx)(U,{label:"AI Resume Rating",percent:75,color:"#A855F7",trailColor:"#EAE2FF"}),(0,a.jsx)(U,{label:"AI Video Rating",percent:75,color:"#FF5B00",trailColor:"#FFEAE1"})]})]}),z=()=>(0,a.jsxs)("div",{className:"h-screen",children:[(0,a.jsx)(F,{}),(0,a.jsx)(v,{children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start",children:[(0,a.jsx)(x,{}),(0,a.jsx)(f,{className:"w-[265px]"}),(0,a.jsx)(R,{})]})}),(0,a.jsx)(D,{})]}),_=()=>{let[e,t]=(0,r.useState)("instructions");return(0,a.jsx)("div",{children:(()=>{switch(e){case"instructions":default:return(0,a.jsx)(d,{onNext:()=>t("questions")});case"questions":return(0,a.jsx)(w,{onNext:()=>t("recording")});case"recording":return(0,a.jsx)(Q,{onNext:()=>t("finishInterview")});case"finishInterview":return(0,a.jsx)(T,{onNext:()=>t("analysis")});case"analysis":return(0,a.jsx)(z,{})}})()})}},6149:(e,t,s)=>{Promise.resolve().then(s.bind(s,4209))},7168:(e,t,s)=>{"use strict";s.d(t,{$:()=>o});var a=s(5155);s(2115);var r=s(4624),i=s(2085),l=s(3999);let n=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:s,size:i,asChild:o=!1,...c}=e,d=o?r.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,l.cn)(n({variant:s,size:i,className:t})),...c})}}},e=>{var t=t=>e(e.s=t);e.O(0,[318,13,766,646,441,684,358],()=>t(6149)),_N_E=e.O()}]);