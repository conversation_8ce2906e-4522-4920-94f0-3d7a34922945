(()=>{var e={};e.id=220,e.ids=[220],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3660:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\app\\\\(auth)\\\\sign-up\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-up\\page.tsx","default")},3873:e=>{"use strict";e.exports=require("path")},4793:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>l,pages:()=>u,routeModule:()=>c,tree:()=>p});var n=r(5239),o=r(8088),s=r(8170),i=r.n(s),a=r(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let p={children:["",{children:["(auth)",{children:["sign-up",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3660)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-up\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,7470)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,8014)),"D:\\Softwares\\Ai bot\\intview-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["D:\\Softwares\\Ai bot\\intview-ai\\app\\(auth)\\sign-up\\page.tsx"],l={require:r,loadChunk:()=>Promise.resolve()},c=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(auth)/sign-up/page",pathname:"/sign-up",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},5414:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(687),o=r(1788),s=r(9360);r(3210);let i=()=>(0,n.jsx)(o.A,{formType:"SIGN_UP",schema:s.m,defaultValues:{email:"",name:"",password:"",confirmPassword:""},onSubmit:e=>Promise.resolve({success:!0,data:e})})},5511:e=>{"use strict";e.exports=require("crypto")},5607:(e,t,r)=>{Promise.resolve().then(r.bind(r,5414))},8655:(e,t,r)=>{Promise.resolve().then(r.bind(r,3660))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[97,423,715,23,976,717],()=>r(4793));module.exports=n})();