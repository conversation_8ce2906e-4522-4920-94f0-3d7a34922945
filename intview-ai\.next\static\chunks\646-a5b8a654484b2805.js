(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[646],{646:(t,e,i)=>{"use strict";i.d(e,{A:()=>n});let n=(0,i(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},839:()=>{},2138:(t,e,i)=>{"use strict";i.d(e,{A:()=>n});let n=(0,i(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2900:(t,e,i)=>{"use strict";let n;i.d(e,{P:()=>rS});var s=i(2115);let r=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],a=new Set(r),o=t=>180*t/Math.PI,l=t=>u(o(Math.atan2(t[1],t[0]))),h={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:l,rotateZ:l,skewX:t=>o(Math.atan(t[1])),skewY:t=>o(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},u=t=>((t%=360)<0&&(t+=360),t),d=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),c=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),p={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:d,scaleY:c,scale:t=>(d(t)+c(t))/2,rotateX:t=>u(o(Math.atan2(t[6],t[5]))),rotateY:t=>u(o(Math.atan2(-t[2],t[0]))),rotateZ:l,rotate:l,skewX:t=>o(Math.atan(t[4])),skewY:t=>o(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function m(t){return+!!t.includes("scale")}function f(t,e){let i,n;if(!t||"none"===t)return m(e);let s=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(s)i=p,n=s;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=h,n=e}if(!n)return m(e);let r=i[e],a=n[1].split(",").map(y);return"function"==typeof r?r(a):a[r]}let g=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return f(i,e)};function y(t){return parseFloat(t.trim())}let v=t=>e=>"string"==typeof e&&e.startsWith(t),x=v("--"),w=v("var(--"),T=t=>!!w(t)&&b.test(t.split("/*")[0].trim()),b=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function P({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}let S=(t,e,i)=>t+(e-t)*i;function A(t){return void 0===t||1===t}function M({scale:t,scaleX:e,scaleY:i}){return!A(t)||!A(e)||!A(i)}function k(t){return M(t)||C(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function C(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function E(t,e,i,n,s){return void 0!==s&&(t=n+s*(t-n)),n+i*(t-n)+e}function V(t,e=0,i=1,n,s){t.min=E(t.min,e,i,n,s),t.max=E(t.max,e,i,n,s)}function D(t,{x:e,y:i}){V(t.x,e.translate,e.scale,e.originPoint),V(t.y,i.translate,i.scale,i.originPoint)}function R(t,e){t.min=t.min+e,t.max=t.max+e}function L(t,e,i,n,s=.5){let r=S(t.min,t.max,s);V(t,e,i,r,n)}function j(t,e){L(t.x,e.x,e.scaleX,e.scale,e.originX),L(t.y,e.y,e.scaleY,e.scale,e.originY)}function F(t,e){return P(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let B=new Set(["width","height","top","left","right","bottom",...r]),O=(t,e,i)=>i>e?e:i<t?t:i,I={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},U={...I,transform:t=>O(0,1,t)},N={...I,default:1},W=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),$=W("deg"),z=W("%"),Y=W("px"),H=W("vh"),X=W("vw"),K={...z,parse:t=>z.parse(t)/100,transform:t=>z.transform(100*t)},_=t=>e=>e.test(t),q=[I,Y,z,$,X,H,{test:t=>"auto"===t,parse:t=>t}],G=t=>q.find(_(t)),Z=()=>{},J=()=>{},Q=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),tt=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,te=t=>t===I||t===Y,ti=new Set(["x","y","z"]),tn=r.filter(t=>!ti.has(t)),ts={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>f(e,"x"),y:(t,{transform:e})=>f(e,"y")};ts.translateX=ts.x,ts.translateY=ts.y;let tr=t=>t,ta={},to=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],tl={value:null,addProjectionMetrics:null};function th(t,e){let i=!1,n=!0,s={delta:0,timestamp:0,isProcessing:!1},r=()=>i=!0,a=to.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,s=!1,r=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function h(e){a.has(e)&&(u.schedule(e),t()),l++,e(o)}let u={schedule:(t,e=!1,r=!1)=>{let o=r&&s?i:n;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{n.delete(t),a.delete(t)},process:t=>{if(o=t,s){r=!0;return}s=!0,[i,n]=[n,i],i.forEach(h),e&&tl.value&&tl.value.frameloop[e].push(l),l=0,i.clear(),s=!1,r&&(r=!1,u.process(t))}};return u}(r,e?i:void 0),t),{}),{setup:o,read:l,resolveKeyframes:h,preUpdate:u,update:d,preRender:c,render:p,postRender:m}=a,f=()=>{let r=ta.useManualTiming?s.timestamp:performance.now();i=!1,ta.useManualTiming||(s.delta=n?1e3/60:Math.max(Math.min(r-s.timestamp,40),1)),s.timestamp=r,s.isProcessing=!0,o.process(s),l.process(s),h.process(s),u.process(s),d.process(s),c.process(s),p.process(s),m.process(s),s.isProcessing=!1,i&&e&&(n=!1,t(f))},g=()=>{i=!0,n=!0,s.isProcessing||t(f)};return{schedule:to.reduce((t,e)=>{let n=a[e];return t[e]=(t,e=!1,s=!1)=>(i||g(),n.schedule(t,e,s)),t},{}),cancel:t=>{for(let e=0;e<to.length;e++)a[to[e]].cancel(t)},state:s,steps:a}}let{schedule:tu,cancel:td,state:tc,steps:tp}=th("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:tr,!0),tm=new Set,tf=!1,tg=!1,ty=!1;function tv(){if(tg){let t=Array.from(tm).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return tn.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}tg=!1,tf=!1,tm.forEach(t=>t.complete(ty)),tm.clear()}function tx(){tm.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(tg=!0)})}class tw{constructor(t,e,i,n,s,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=s,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(tm.add(this),tf||(tf=!0,tu.read(tx),tu.resolveKeyframes(tv))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let s=n?.get(),r=t[t.length-1];if(void 0!==s)t[0]=s;else if(i&&e){let n=i.readValue(e,r);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=r),n&&void 0===s&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),tm.delete(this)}cancel(){"scheduled"===this.state&&(tm.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tT=t=>/^0[^.\s]+$/u.test(t),tb=t=>Math.round(1e5*t)/1e5,tP=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tS=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tA=(t,e)=>i=>!!("string"==typeof i&&tS.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tM=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[s,r,a,o]=n.match(tP);return{[t]:parseFloat(s),[e]:parseFloat(r),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},tk=t=>O(0,255,t),tC={...I,transform:t=>Math.round(tk(t))},tE={test:tA("rgb","red"),parse:tM("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+tC.transform(t)+", "+tC.transform(e)+", "+tC.transform(i)+", "+tb(U.transform(n))+")"},tV={test:tA("#"),parse:function(t){let e="",i="",n="",s="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),s=t.substring(4,5),e+=e,i+=i,n+=n,s+=s),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:s?parseInt(s,16)/255:1}},transform:tE.transform},tD={test:tA("hsl","hue"),parse:tM("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+z.transform(tb(e))+", "+z.transform(tb(i))+", "+tb(U.transform(n))+")"},tR={test:t=>tE.test(t)||tV.test(t)||tD.test(t),parse:t=>tE.test(t)?tE.parse(t):tD.test(t)?tD.parse(t):tV.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tE.transform(t):tD.transform(t),getAnimatableNone:t=>{let e=tR.parse(t);return e.alpha=0,tR.transform(e)}},tL=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tj="number",tF="color",tB=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tO(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},s=[],r=0,a=e.replace(tB,t=>(tR.test(t)?(n.color.push(r),s.push(tF),i.push(tR.parse(t))):t.startsWith("var(")?(n.var.push(r),s.push("var"),i.push(t)):(n.number.push(r),s.push(tj),i.push(parseFloat(t))),++r,"${}")).split("${}");return{values:i,split:a,indexes:n,types:s}}function tI(t){return tO(t).values}function tU(t){let{split:e,types:i}=tO(t),n=e.length;return t=>{let s="";for(let r=0;r<n;r++)if(s+=e[r],void 0!==t[r]){let e=i[r];e===tj?s+=tb(t[r]):e===tF?s+=tR.transform(t[r]):s+=t[r]}return s}}let tN=t=>"number"==typeof t?0:tR.test(t)?tR.getAnimatableNone(t):t,tW={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(tP)?.length||0)+(t.match(tL)?.length||0)>0},parse:tI,createTransformer:tU,getAnimatableNone:function(t){let e=tI(t);return tU(t)(e.map(tN))}},t$=new Set(["brightness","contrast","saturate","opacity"]);function tz(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(tP)||[];if(!n)return t;let s=i.replace(n,""),r=+!!t$.has(e);return n!==i&&(r*=100),e+"("+r+s+")"}let tY=/\b([a-z-]*)\(.*?\)/gu,tH={...tW,getAnimatableNone:t=>{let e=t.match(tY);return e?e.map(tz).join(" "):t}},tX={...I,transform:Math.round},tK={borderWidth:Y,borderTopWidth:Y,borderRightWidth:Y,borderBottomWidth:Y,borderLeftWidth:Y,borderRadius:Y,radius:Y,borderTopLeftRadius:Y,borderTopRightRadius:Y,borderBottomRightRadius:Y,borderBottomLeftRadius:Y,width:Y,maxWidth:Y,height:Y,maxHeight:Y,top:Y,right:Y,bottom:Y,left:Y,padding:Y,paddingTop:Y,paddingRight:Y,paddingBottom:Y,paddingLeft:Y,margin:Y,marginTop:Y,marginRight:Y,marginBottom:Y,marginLeft:Y,backgroundPositionX:Y,backgroundPositionY:Y,rotate:$,rotateX:$,rotateY:$,rotateZ:$,scale:N,scaleX:N,scaleY:N,scaleZ:N,skew:$,skewX:$,skewY:$,distance:Y,translateX:Y,translateY:Y,translateZ:Y,x:Y,y:Y,z:Y,perspective:Y,transformPerspective:Y,opacity:U,originX:K,originY:K,originZ:Y,zIndex:tX,fillOpacity:U,strokeOpacity:U,numOctaves:tX},t_={...tK,color:tR,backgroundColor:tR,outlineColor:tR,fill:tR,stroke:tR,borderColor:tR,borderTopColor:tR,borderRightColor:tR,borderBottomColor:tR,borderLeftColor:tR,filter:tH,WebkitFilter:tH},tq=t=>t_[t];function tG(t,e){let i=tq(t);return i!==tH&&(i=tW),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let tZ=new Set(["auto","none","0"]);class tJ extends tw{constructor(t,e,i,n,s){super(t,e,i,n,s,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&T(n=n.trim())){let s=function t(e,i,n=1){J(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[s,r]=function(t){let e=tt.exec(t);if(!e)return[,];let[,i,n,s]=e;return[`--${i??n}`,s]}(e);if(!s)return;let a=window.getComputedStyle(i).getPropertyValue(s);if(a){let t=a.trim();return Q(t)?parseFloat(t):t}return T(r)?t(r,i,n+1):r}(n,e.current);void 0!==s&&(t[i]=s),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!B.has(i)||2!==t.length)return;let[n,s]=t,r=G(n),a=G(s);if(r!==a)if(te(r)&&te(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else ts[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||tT(n)))&&i.push(e)}i.length&&function(t,e,i){let n,s=0;for(;s<t.length&&!n;){let e=t[s];"string"==typeof e&&!tZ.has(e)&&tO(e).values.length&&(n=t[s]),s++}if(n&&i)for(let s of e)t[s]=tG(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ts[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let s=i.length-1,r=i[s];i[s]=ts[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let tQ=t=>!!(t&&t.getVelocity);function t0(){n=void 0}let t1={now:()=>(void 0===n&&t1.set(tc.isProcessing||ta.useManualTiming?tc.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(t0)}};function t2(t,e){-1===t.indexOf(e)&&t.push(e)}function t5(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class t3{constructor(){this.subscriptions=[]}add(t){return t2(this.subscriptions,t),()=>t5(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let s=0;s<n;s++){let n=this.subscriptions[s];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let t9=t=>!isNaN(parseFloat(t)),t4={current:void 0};class t6{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=t=>{let e=t1.now();if(this.updatedAt!==e&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=t1.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=t9(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new t3);let i=this.events[t].add(e);return"change"===t?()=>{i(),tu.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return t4.current&&t4.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=t1.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function t8(t,e){return new t6(t,e)}let t7=[...q,tR,tW],et=t=>t7.find(_(t)),{schedule:ee}=th(queueMicrotask,!1),ei={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},en={};for(let t in ei)en[t]={isEnabled:e=>ei[t].some(t=>!!e[t])};let es=()=>({translate:0,scale:1,origin:0,originPoint:0}),er=()=>({x:es(),y:es()}),ea=()=>({min:0,max:0}),eo=()=>({x:ea(),y:ea()}),el="undefined"!=typeof window,eh={current:null},eu={current:!1},ed=new WeakMap;function ec(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function ep(t){return"string"==typeof t||Array.isArray(t)}let em=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ef=["initial",...em];function eg(t){return ec(t.animate)||ef.some(e=>ep(t[e]))}function ey(t){return!!(eg(t)||t.variants)}function ev(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function ex(t,e,i,n){if("function"==typeof e){let[s,r]=ev(n);e=e(void 0!==i?i:t.custom,s,r)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[s,r]=ev(n);e=e(void 0!==i?i:t.custom,s,r)}return e}let ew=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class eT{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:s,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tw,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=t1.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,tu.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=r;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.blockInitialAnimation=!!s,this.isControllingVariants=eg(e),this.isVariantNode=ey(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in u){let e=u[t];void 0!==o[t]&&tQ(e)&&e.set(o[t])}}mount(t){this.current=t,ed.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),eu.current||function(){if(eu.current=!0,el)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>eh.current=t.matches;t.addEventListener("change",e),e()}else eh.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||eh.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),td(this.notifyUpdate),td(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}addChild(t){this.children.add(t),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(t)}removeChild(t){this.children.delete(t),this.enteringChildren&&this.enteringChildren.delete(t)}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=a.has(t);n&&this.onBindTransform&&this.onBindTransform();let s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&tu.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in en){let e=en[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):eo()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<ew.length;e++){let i=ew[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let s=e[n],r=i[n];if(tQ(s))t.addValue(n,s);else if(tQ(r))t.addValue(n,t8(s,{owner:t}));else if(r!==s)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(n);t.addValue(n,t8(void 0!==e?e:s,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=t8(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(Q(i)||tT(i))?i=parseFloat(i):!et(i)&&tW.test(e)&&(i=tG(t,e)),this.setBaseTarget(t,tQ(i)?i.get():i)),tQ(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=ex(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||tQ(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new t3),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}scheduleRenderMicrotask(){ee.render(this.render)}}class eb extends eT{constructor(){super(...arguments),this.KeyframeResolver=tJ}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;tQ(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}let eP=(t,e)=>e&&"number"==typeof t?e.transform(t):t,eS={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},eA=r.length;function eM(t,e,i){let{style:n,vars:s,transformOrigin:o}=t,l=!1,h=!1;for(let t in e){let i=e[t];if(a.has(t)){l=!0;continue}if(x(t)){s[t]=i;continue}{let e=eP(i,tK[t]);t.startsWith("origin")?(h=!0,o[t]=e):n[t]=e}}if(!e.transform&&(l||i?n.transform=function(t,e,i){let n="",s=!0;for(let a=0;a<eA;a++){let o=r[a],l=t[o];if(void 0===l)continue;let h=!0;if(!(h="number"==typeof l?l===+!!o.startsWith("scale"):0===parseFloat(l))||i){let t=eP(l,tK[o]);if(!h){s=!1;let e=eS[o]||o;n+=`${e}(${t}) `}i&&(e[o]=t)}}return n=n.trim(),i?n=i(e,s?"":n):s&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),h){let{originX:t="50%",originY:e="50%",originZ:i=0}=o;n.transformOrigin=`${t} ${e} ${i}`}}function ek(t,{style:e,vars:i},n,s){let r,a=t.style;for(r in e)a[r]=e[r];for(r in s?.applyProjectionStyles(a,n),i)a.setProperty(r,i[r])}let eC={};function eE(t,{layout:e,layoutId:i}){return a.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!eC[t]||"opacity"===t)}function eV(t,e,i){let{style:n}=t,s={};for(let r in n)(tQ(n[r])||e.style&&tQ(e.style[r])||eE(r,t)||i?.getValue(r)?.liveStyle!==void 0)&&(s[r]=n[r]);return s}class eD extends eb{constructor(){super(...arguments),this.type="html",this.renderInstance=ek}readValueFromInstance(t,e){if(a.has(e))return this.projection?.isProjecting?m(e):g(t,e);{let i=window.getComputedStyle(t),n=(x(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return F(t,e)}build(t,e,i){eM(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return eV(t,e,i)}}let eR=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),eL={offset:"stroke-dashoffset",array:"stroke-dasharray"},ej={offset:"strokeDashoffset",array:"strokeDasharray"};function eF(t,{attrX:e,attrY:i,attrScale:n,pathLength:s,pathSpacing:r=1,pathOffset:a=0,...o},l,h,u){if(eM(t,o,h),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=u?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==n&&(d.scale=n),void 0!==s&&function(t,e,i=1,n=0,s=!0){t.pathLength=1;let r=s?eL:ej;t[r.offset]=Y.transform(-n);let a=Y.transform(e),o=Y.transform(i);t[r.array]=`${a} ${o}`}(d,s,r,a,!1)}let eB=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),eO=t=>"string"==typeof t&&"svg"===t.toLowerCase();function eI(t,e,i){let n=eV(t,e,i);for(let i in t)(tQ(t[i])||tQ(e[i]))&&(n[-1!==r.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}class eU extends eb{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=eo}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(a.has(e)){let t=tq(e);return t&&t.default||0}return e=eB.has(e)?e:eR(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return eI(t,e,i)}build(t,e,i){eF(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){for(let i in ek(t,e,void 0,n),e.attrs)t.setAttribute(eB.has(i)?i:eR(i),e.attrs[i])}mount(t){this.isSVGTag=eO(t.tagName),super.mount(t)}}let eN=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function eW(t){if("string"!=typeof t||t.includes("-"));else if(eN.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var e$=i(5155);let ez=(0,s.createContext)({}),eY=(0,s.createContext)({strict:!1}),eH=(0,s.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),eX=(0,s.createContext)({});function eK(t){return Array.isArray(t)?t.join(" "):t}let e_=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function eq(t,e,i){for(let n in e)tQ(e[n])||eE(n,i)||(t[n]=e[n])}let eG=()=>({...e_(),attrs:{}}),eZ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function eJ(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||eZ.has(t)}let eQ=t=>!eJ(t);try{!function(t){"function"==typeof t&&(eQ=e=>e.startsWith("on")?!eJ(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let e0=(0,s.createContext)(null);function e1(t){return tQ(t)?t.get():t}let e2=t=>(e,i)=>{let n=(0,s.useContext)(eX),r=(0,s.useContext)(e0),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,s){return{latestValues:function(t,e,i,n){let s={},r=n(t,{});for(let t in r)s[t]=e1(r[t]);let{initial:a,animate:o}=t,l=eg(t),h=ey(t);e&&h&&!l&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===o&&(o=e.animate));let u=!!i&&!1===i.initial,d=(u=u||!1===a)?o:a;if(d&&"boolean"!=typeof d&&!ec(d)){let e=Array.isArray(d)?d:[d];for(let i=0;i<e.length;i++){let n=ex(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=u?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(i,n,s,t),renderState:e()}})(t,e,n,r);return i?a():function(t){let e=(0,s.useRef)(null);return null===e.current&&(e.current=t()),e.current}(a)},e5=e2({scrapeMotionValuesFromProps:eV,createRenderState:e_}),e3=e2({scrapeMotionValuesFromProps:eI,createRenderState:eG}),e9=Symbol.for("motionComponentSymbol");function e4(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let e6="data-"+eR("framerAppearId"),e8=(0,s.createContext)({}),e7=el?s.useLayoutEffect:s.useEffect;function it(t){var e,i;let{forwardMotionProps:n=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0;r&&function(t){for(let e in t)en[e]={...en[e],...t[e]}}(r);let o=eW(t)?e3:e5;function l(e,i){var r,l,h;let u,d={...(0,s.useContext)(eH),...e,layoutId:function(t){let{layoutId:e}=t,i=(0,s.useContext)(ez).id;return i&&void 0!==e?i+"-"+e:e}(e)},{isStatic:c}=d,p=function(t){let{initial:e,animate:i}=function(t,e){if(eg(t)){let{initial:e,animate:i}=t;return{initial:!1===e||ep(e)?e:void 0,animate:ep(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,s.useContext)(eX));return(0,s.useMemo)(()=>({initial:e,animate:i}),[eK(e),eK(i)])}(e),m=o(e,c);if(!c&&el){l=0,h=0,(0,s.useContext)(eY).strict;let e=function(t){let{drag:e,layout:i}=en;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(d);u=e.MeasureLayout,p.visualElement=function(t,e,i,n,r){let{visualElement:a}=(0,s.useContext)(eX),o=(0,s.useContext)(eY),l=(0,s.useContext)(e0),h=(0,s.useContext)(eH).reducedMotion,u=(0,s.useRef)(null);n=n||o.renderer,!u.current&&n&&(u.current=n(t,{visualState:e,parent:a,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:h}));let d=u.current,c=(0,s.useContext)(e8);d&&!d.projection&&r&&("html"===d.type||"svg"===d.type)&&function(t,e,i,n){let{layoutId:s,layout:r,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:s,layout:r,alwaysMeasureLayout:!!a||o&&e4(o),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:n,crossfade:u,layoutScroll:l,layoutRoot:h})}(u.current,i,r,c);let p=(0,s.useRef)(!1);(0,s.useInsertionEffect)(()=>{d&&p.current&&d.update(i,l)});let m=i[e6],f=(0,s.useRef)(!!m&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return e7(()=>{d&&(p.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),d.scheduleRenderMicrotask(),f.current&&d.animationState&&d.animationState.animateChanges())}),(0,s.useEffect)(()=>{d&&(!f.current&&d.animationState&&d.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),f.current=!1),d.enteringChildren=void 0)}),d}(t,m,d,a,e.ProjectionNode)}return(0,e$.jsxs)(eX.Provider,{value:p,children:[u&&p.visualElement?(0,e$.jsx)(u,{visualElement:p.visualElement,...d}):null,function(t,e,i,{latestValues:n},r,a=!1){let o=(eW(t)?function(t,e,i,n){let r=(0,s.useMemo)(()=>{let i=eG();return eF(i,e,eO(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};eq(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return eq(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,s.useMemo)(()=>{let i=e_();return eM(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(e,n,r,t),l=function(t,e,i){let n={};for(let s in t)("values"!==s||"object"!=typeof t.values)&&(eQ(s)||!0===i&&eJ(s)||!e&&!eJ(s)||t.draggable&&s.startsWith("onDrag"))&&(n[s]=t[s]);return n}(e,"string"==typeof t,a),h=t!==s.Fragment?{...l,...o,ref:i}:{},{children:u}=e,d=(0,s.useMemo)(()=>tQ(u)?u.get():u,[u]);return(0,s.createElement)(t,{...h,children:d})}(t,e,(r=p.visualElement,(0,s.useCallback)(t=>{t&&m.onMount&&m.onMount(t),r&&(t?r.mount(t):r.unmount()),i&&("function"==typeof i?i(t):e4(i)&&(i.current=t))},[r])),m,c,n)]})}l.displayName="motion.".concat("string"==typeof t?t:"create(".concat(null!=(i=null!=(e=t.displayName)?e:t.name)?i:"",")"));let h=(0,s.forwardRef)(l);return h[e9]=t,h}function ie(t,e,i){let n=t.getProps();return ex(n,e,void 0!==i?i:n.custom,t)}function ii(t,e){return t?.[e]??t?.default??t}let is=t=>Array.isArray(t);function ir(t,e){let i=t.getValue("willChange");if(tQ(i)&&i.add)return i.add(e);if(!i&&ta.WillChange){let i=new ta.WillChange("auto");t.addValue("willChange",i),i.add(e)}}function ia(t){t.duration=0,t.type}let io=(t,e)=>i=>e(t(i)),il=(...t)=>t.reduce(io),ih=t=>1e3*t,iu=t=>t/1e3,id={layout:0,mainThread:0,waapi:0};function ic(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function ip(t,e){return i=>i>0?e:t}let im=(t,e,i)=>{let n=t*t,s=i*(e*e-n)+n;return s<0?0:Math.sqrt(s)},ig=[tV,tE,tD],iy=t=>ig.find(e=>e.test(t));function iv(t){let e=iy(t);if(Z(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!e)return!1;let i=e.parse(t);return e===tD&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let s=0,r=0,a=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,o=2*i-n;s=ic(o,n,t+1/3),r=ic(o,n,t),a=ic(o,n,t-1/3)}else s=r=a=i;return{red:Math.round(255*s),green:Math.round(255*r),blue:Math.round(255*a),alpha:n}}(i)),i}let ix=(t,e)=>{let i=iv(t),n=iv(e);if(!i||!n)return ip(t,e);let s={...i};return t=>(s.red=im(i.red,n.red,t),s.green=im(i.green,n.green,t),s.blue=im(i.blue,n.blue,t),s.alpha=S(i.alpha,n.alpha,t),tE.transform(s))},iw=new Set(["none","hidden"]);function iT(t,e){return i=>S(t,e,i)}function ib(t){return"number"==typeof t?iT:"string"==typeof t?T(t)?ip:tR.test(t)?ix:iA:Array.isArray(t)?iP:"object"==typeof t?tR.test(t)?ix:iS:ip}function iP(t,e){let i=[...t],n=i.length,s=t.map((t,i)=>ib(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=s[e](t);return i}}function iS(t,e){let i={...t,...e},n={};for(let s in i)void 0!==t[s]&&void 0!==e[s]&&(n[s]=ib(t[s])(t[s],e[s]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let iA=(t,e)=>{let i=tW.createTransformer(e),n=tO(t),s=tO(e);return n.indexes.var.length===s.indexes.var.length&&n.indexes.color.length===s.indexes.color.length&&n.indexes.number.length>=s.indexes.number.length?iw.has(t)&&!s.values.length||iw.has(e)&&!n.values.length?function(t,e){return iw.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):il(iP(function(t,e){let i=[],n={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){let r=e.types[s],a=t.indexes[r][n[r]],o=t.values[a]??0;i[s]=o,n[r]++}return i}(n,s),s.values),i):(Z(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),ip(t,e))};function iM(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?S(t,e,i):ib(t)(t,e)}let ik=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>tu.update(e,t),stop:()=>td(e),now:()=>tc.isProcessing?tc.timestamp:t1.now()}},iC=(t,e,i=10)=>{let n="",s=Math.max(Math.round(e/i),2);for(let e=0;e<s;e++)n+=Math.round(1e4*t(e/(s-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function iE(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function iV(t,e,i){var n,s;let r=Math.max(e-5,0);return n=i-t(r),(s=e-r)?1e3/s*n:0}let iD={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function iR(t,e){return t*Math.sqrt(1-e*e)}let iL=["duration","bounce"],ij=["stiffness","damping","mass"];function iF(t,e){return e.some(e=>void 0!==t[e])}function iB(t=iD.visualDuration,e=iD.bounce){let i,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:s,restDelta:r}=n,a=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],l={done:!1,value:a},{stiffness:h,damping:u,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:iD.velocity,stiffness:iD.stiffness,damping:iD.damping,mass:iD.mass,isResolvedFromDuration:!1,...t};if(!iF(t,ij)&&iF(t,iL))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,s=2*O(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:iD.mass,stiffness:n,damping:s}}else{let i=function({duration:t=iD.duration,bounce:e=iD.bounce,velocity:i=iD.velocity,mass:n=iD.mass}){let s,r;Z(t<=ih(iD.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let a=1-e;a=O(iD.minDamping,iD.maxDamping,a),t=O(iD.minDuration,iD.maxDuration,iu(t)),a<1?(s=e=>{let n=e*a,s=n*t;return .001-(n-i)/iR(e,a)*Math.exp(-s)},r=e=>{let n=e*a*t,r=Math.pow(a,2)*Math.pow(e,2)*t,o=Math.exp(-n),l=iR(Math.pow(e,2),a);return(n*i+i-r)*o*(-s(e)+.001>0?-1:1)/l}):(s=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let o=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(s,r,5/t);if(t=ih(t),isNaN(o))return{stiffness:iD.stiffness,damping:iD.damping,duration:t};{let e=Math.pow(o,2)*n;return{stiffness:e,damping:2*a*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:iD.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-iu(n.velocity||0)}),f=p||0,g=u/(2*Math.sqrt(h*d)),y=o-a,v=iu(Math.sqrt(h/d)),x=5>Math.abs(y);if(s||(s=x?iD.restSpeed.granular:iD.restSpeed.default),r||(r=x?iD.restDelta.granular:iD.restDelta.default),g<1){let t=iR(v,g);i=e=>o-Math.exp(-g*v*e)*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>o-Math.exp(-v*t)*(y+(f+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),n=Math.min(t*e,300);return o-i*((f+g*v*y)*Math.sinh(n)+t*y*Math.cosh(n))/t}}let w={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let n=0===t?f:0;g<1&&(n=0===t?ih(f):iV(i,t,e));let a=Math.abs(o-e)<=r;l.done=Math.abs(n)<=s&&a}return l.value=l.done?o:e,l},toString:()=>{let t=Math.min(iE(w),2e4),e=iC(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function iO({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:s=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:l,restDelta:h=.5,restSpeed:u}){let d,c,p=t[0],m={done:!1,value:p},f=t=>void 0!==o&&t<o||void 0!==l&&t>l,g=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l,y=i*e,v=p+y,x=void 0===a?v:a(v);x!==v&&(y=x-p);let w=t=>-y*Math.exp(-t/n),T=t=>x+w(t),b=t=>{let e=w(t),i=T(t);m.done=Math.abs(e)<=h,m.value=m.done?x:i},P=t=>{f(m.value)&&(d=t,c=iB({keyframes:[m.value,g(m.value)],velocity:iV(T,t,m.value),damping:s,stiffness:r,restDelta:h,restSpeed:u}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,b(t),P(t)),void 0!==d&&t>=d)?c.next(t-d):(e||b(t),m)}}}iB.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),s=Math.min(iE(n),2e4);return{type:"keyframes",ease:t=>n.next(s*t).value/e,duration:iu(s)}}(t,100,iB);return t.ease=e.ease,t.duration=ih(e.duration),t.type="keyframes",t};let iI=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function iU(t,e,i,n){if(t===e&&i===n)return tr;let s=e=>(function(t,e,i,n,s){let r,a,o=0;do(r=iI(a=e+(i-e)/2,n,s)-t)>0?i=a:e=a;while(Math.abs(r)>1e-7&&++o<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:iI(s(t),e,n)}let iN=iU(.42,0,1,1),iW=iU(0,0,.58,1),i$=iU(.42,0,.58,1),iz=t=>Array.isArray(t)&&"number"!=typeof t[0],iY=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,iH=t=>e=>1-t(1-e),iX=iU(.33,1.53,.69,.99),iK=iH(iX),i_=iY(iK),iq=t=>(t*=2)<1?.5*iK(t):.5*(2-Math.pow(2,-10*(t-1))),iG=t=>1-Math.sin(Math.acos(t)),iZ=iH(iG),iJ=iY(iG),iQ=t=>Array.isArray(t)&&"number"==typeof t[0],i0={linear:tr,easeIn:iN,easeInOut:i$,easeOut:iW,circIn:iG,circInOut:iJ,circOut:iZ,backIn:iK,backInOut:i_,backOut:iX,anticipate:iq},i1=t=>"string"==typeof t,i2=t=>{if(iQ(t)){J(4===t.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[e,i,n,s]=t;return iU(e,i,n,s)}return i1(t)?(J(void 0!==i0[t],`Invalid easing type '${t}'`,"invalid-easing-type"),i0[t]):t},i5=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};function i3({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){var s;let r=iz(n)?n.map(i2):i2(n),a={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:n,mixer:s}={}){let r=t.length;if(J(r===e.length,"Both input and output ranges must be the same length","range-length"),1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];let a=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,i){let n=[],s=i||ta.mix||iM,r=t.length-1;for(let i=0;i<r;i++){let r=s(t[i],t[i+1]);e&&(r=il(Array.isArray(e)?e[i]||tr:e,r)),n.push(r)}return n}(e,n,s),l=o.length,h=i=>{if(a&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let s=i5(t[n],t[n+1],i);return o[n](s)};return i?e=>h(O(t[0],t[r-1],e)):h}((s=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let s=i5(0,e,n);t.push(S(i,1,s))}}(e,t.length-1),e}(e),s.map(e=>e*t)),e,{ease:Array.isArray(r)?r:e.map(()=>r||i$).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(a.value=o(e),a.done=e>=t,a)}}let i9=t=>null!==t;function i4(t,{repeat:e,repeatType:i="loop"},n,s=1){let r=t.filter(i9),a=s<0||e&&"loop"!==i&&e%2==1?0:r.length-1;return a&&void 0!==n?n:r[a]}let i6={decay:iO,inertia:iO,tween:i3,keyframes:i3,spring:iB};function i8(t){"string"==typeof t.type&&(t.type=i6[t.type])}class i7{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let nt=t=>t/100;class ne extends i7{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==t1.now()&&this.tick(t1.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},id.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;i8(t);let{type:e=i3,repeat:i=0,repeatDelay:n=0,repeatType:s,velocity:r=0}=t,{keyframes:a}=t,o=e||i3;o!==i3&&"number"!=typeof a[0]&&(this.mixKeyframes=il(nt,iM(a[0],a[1])),a=[0,100]);let l=o({...t,keyframes:a});"mirror"===s&&(this.mirroredGenerator=o({...t,keyframes:[...a].reverse(),velocity:-r})),null===l.calculatedDuration&&(l.calculatedDuration=iE(l));let{calculatedDuration:h}=l;this.calculatedDuration=h,this.resolvedDuration=h+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:s,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:h,repeat:u,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,x=i;if(u){let t=Math.min(this.currentTime,n)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,u+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/a)):"mirror"===d&&(x=r)),v=O(0,1,i)*a}let w=y?{done:!1,value:h[0]}:x.next(v);s&&(w.value=s(w.value));let{done:T}=w;y||null===o||(T=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let b=null===this.holdTime&&("finished"===this.state||"running"===this.state&&T);return b&&p!==iO&&(w.value=i4(h,this.options,f,this.speed)),m&&m(w.value),b&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return iu(this.calculatedDuration)}get time(){return iu(this.currentTime)}set time(t){t=ih(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(t1.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=iu(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=ik,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(t1.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,id.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let ni=t=>t.startsWith("--");function nn(t){let e;return()=>(void 0===e&&(e=t()),e)}let ns=nn(()=>void 0!==window.ScrollTimeline),nr={},na=function(t,e){let i=nn(t);return()=>nr[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),no=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,nl={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:no([0,.65,.55,1]),circOut:no([.55,0,1,.45]),backIn:no([.31,.01,.66,-.59]),backOut:no([.33,1.53,.69,.99])};function nh(t){return"function"==typeof t&&"applyToOptions"in t}class nu extends i7{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:s,allowFlatten:r=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=!!s,this.allowFlatten=r,this.options=t,J("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:t,...e}){return nh(t)&&na()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:s=300,repeat:r=0,repeatType:a="loop",ease:o="easeOut",times:l}={},h){let u={[e]:i};l&&(u.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?na()?iC(e,i):"ease-out":iQ(e)?no(e):Array.isArray(e)?e.map(e=>t(e,i)||nl.easeOut):nl[e]}(o,s);Array.isArray(d)&&(u.easing=d),tl.value&&id.waapi++;let c={delay:n,duration:s,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:r+1,direction:"reverse"===a?"alternate":"normal"};h&&(c.pseudoElement=h);let p=t.animate(u,c);return tl.value&&p.finished.finally(()=>{id.waapi--}),p}(e,i,n,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){let t=i4(n,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){ni(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return iu(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return iu(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=ih(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&ns())?(this.animation.timeline=t,tr):e(this)}}let nd={anticipate:iq,backInOut:i_,circInOut:iJ};class nc extends nu{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in nd&&(t.ease=nd[t.ease])}(t),i8(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:s,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let a=new ne({...r,autoplay:!1}),o=ih(this.finishedTime??this.time);e.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}let np=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tW.test(t)||"0"===t)&&!t.startsWith("url(")),nm=new Set(["opacity","clipPath","filter","transform"]),nf=nn(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class ng extends i7{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:r="loop",keyframes:a,name:o,motionValue:l,element:h,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=t1.now();let d={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:s,repeatType:r,name:o,motionValue:l,element:h,...u},c=h?.KeyframeResolver||tw;this.keyframeResolver=new c(a,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),o,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:s,type:r,velocity:a,delay:o,isHandoff:l,onUpdate:h}=i;this.resolvedAt=t1.now(),!function(t,e,i,n){let s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],a=np(s,e),o=np(r,e);return Z(a===o,`You are trying to animate ${e} from "${s}" to "${r}". "${a?r:s}" is not an animatable value.`,"value-not-animatable"),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||nh(i))&&n)}(t,s,r,a)&&((ta.instantAnimations||!o)&&h?.(i4(t,i,e)),t[0]=t[t.length-1],ia(i),i.repeat=0);let u={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:s,damping:r,type:a}=t;if(!(e?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return nf()&&i&&nm.has(i)&&("transform"!==i||!l)&&!o&&!n&&"mirror"!==s&&0!==r&&"inertia"!==a}(u)?new nc({...u,element:u.motionValue.owner.current}):new ne(u);d.finished.then(()=>this.notifyFinished()).catch(tr),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),ty=!0,tx(),tv(),ty=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let ny=t=>null!==t,nv={type:"spring",stiffness:500,damping:25,restSpeed:10},nx=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),nw={type:"keyframes",duration:.8},nT={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},nb=(t,{keyframes:e})=>e.length>2?nw:a.has(t)?t.startsWith("scale")?nx(e[1]):nv:nT,nP=(t,e,i,n={},s,r)=>a=>{let o=ii(n,t)||{},l=o.delay||n.delay||0,{elapsed:h=0}=n;h-=ih(l);let u={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...o,delay:-h,onUpdate:t=>{e.set(t),o.onUpdate&&o.onUpdate(t)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:t,motionValue:e,element:r?void 0:s};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:s,repeat:r,repeatType:a,repeatDelay:o,from:l,elapsed:h,...u}){return!!Object.keys(u).length}(o)&&Object.assign(u,nb(t,u)),u.duration&&(u.duration=ih(u.duration)),u.repeatDelay&&(u.repeatDelay=ih(u.repeatDelay)),void 0!==u.from&&(u.keyframes[0]=u.from);let d=!1;if(!1!==u.type&&(0!==u.duration||u.repeatDelay)||(ia(u),0===u.delay&&(d=!0)),(ta.instantAnimations||ta.skipAnimations)&&(d=!0,ia(u),u.delay=0),u.allowFlatten=!o.type&&!o.ease,d&&!r&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let s=t.filter(ny),r=e&&"loop"!==i&&e%2==1?0:s.length-1;return s[r]}(u.keyframes,o);if(void 0!==t)return void tu.update(()=>{u.onUpdate(t),u.onComplete()})}return o.isSync?new ne(u):new ng(u)};function nS(t,e,{delay:i=0,transitionOverride:n,type:s}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:a,...o}=e;n&&(r=n);let l=[],h=s&&t.animationState&&t.animationState.getState()[s];for(let e in o){let n=t.getValue(e,t.latestValues[e]??null),s=o[e];if(void 0===s||h&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(h,e))continue;let a={delay:i,...ii(r||{},e)},u=n.get();if(void 0!==u&&!n.isAnimating&&!Array.isArray(s)&&s===u&&!a.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let i=t.props[e6];if(i){let t=window.MotionHandoffAnimation(i,e,tu);null!==t&&(a.startTime=t,d=!0)}}ir(t,e),n.start(nP(e,n,s,t.shouldReduceMotion&&B.has(e)?{type:!1}:a,t,d));let c=n.animation;c&&l.push(c)}return a&&Promise.all(l).then(()=>{tu.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:n={},...s}=ie(t,e)||{};for(let e in s={...s,...i}){var r;let i=is(r=s[e])?r[r.length-1]||0:r;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,t8(i))}}(t,a)})}),l}function nA(t,e,i,n=0,s=1){let r=Array.from(t).sort((t,e)=>t.sortNodePosition(e)).indexOf(e),a=t.size,o=(a-1)*n;return"function"==typeof i?i(r,a):1===s?r*n:o-r*n}function nM(t,e,i={}){let n=ie(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(s=i.transitionOverride);let r=n?()=>Promise.all(nS(t,n,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:r=0,staggerChildren:a,staggerDirection:o}=s;return function(t,e,i=0,n=0,s=0,r=1,a){let o=[];for(let l of t.variantChildren)l.notify("AnimationStart",e),o.push(nM(l,e,{...a,delay:i+("function"==typeof n?0:n)+nA(t.variantChildren,l,n,s,r)}).then(()=>l.notify("AnimationComplete",e)));return Promise.all(o)}(t,e,n,r,a,o,i)}:()=>Promise.resolve(),{when:o}=s;if(!o)return Promise.all([r(),a(i.delay)]);{let[t,e]="beforeChildren"===o?[r,a]:[a,r];return t().then(()=>e())}}function nk(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}let nC=ef.length,nE=[...em].reverse(),nV=em.length;function nD(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nR(){return{animate:nD(!0),whileInView:nD(),whileHover:nD(),whileTap:nD(),whileDrag:nD(),whileFocus:nD(),exit:nD()}}class nL{constructor(t){this.isMounted=!1,this.node=t}update(){}}class nj extends nL{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>nM(t,e,i)));else if("string"==typeof e)n=nM(t,e,i);else{let s="function"==typeof e?ie(t,e,i.custom):e;n=Promise.all(nS(t,s,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=nR(),n=!0,s=e=>(i,n)=>{let s=ie(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(s){let{transition:t,transitionEnd:e,...n}=s;i={...i,...n,...e}}return i};function r(r){let{props:a}=t,o=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<nC;t++){let n=ef[t],s=e.props[n];(ep(s)||!1===s)&&(i[n]=s)}return i}(t.parent)||{},l=[],h=new Set,u={},d=1/0;for(let e=0;e<nV;e++){var c,p;let m=nE[e],f=i[m],g=void 0!==a[m]?a[m]:o[m],y=ep(g),v=m===r?f.isActive:null;!1===v&&(d=e);let x=g===o[m]&&g!==a[m]&&y;if(x&&n&&t.manuallyAnimateOnMount&&(x=!1),f.protectedKeys={...u},!f.isActive&&null===v||!g&&!f.prevProp||ec(g)||"boolean"==typeof g)continue;let w=(c=f.prevProp,"string"==typeof(p=g)?p!==c:!!Array.isArray(p)&&!nk(p,c)),T=w||m===r&&f.isActive&&!x&&y||e>d&&y,b=!1,P=Array.isArray(g)?g:[g],S=P.reduce(s(m),{});!1===v&&(S={});let{prevResolvedValues:A={}}=f,M={...A,...S},k=e=>{T=!0,h.has(e)&&(b=!0,h.delete(e)),f.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in M){let e=S[t],i=A[t];if(u.hasOwnProperty(t))continue;let n=!1;(is(e)&&is(i)?nk(e,i):e===i)?void 0!==e&&h.has(t)?k(t):f.protectedKeys[t]=!0:null!=e?k(t):h.add(t)}f.prevProp=g,f.prevResolvedValues=S,f.isActive&&(u={...u,...S}),n&&t.blockInitialAnimation&&(T=!1);let C=x&&w,E=!C||b;T&&E&&l.push(...P.map(e=>{let i={type:m};if("string"==typeof e&&n&&!C&&t.manuallyAnimateOnMount&&t.parent){let{parent:n}=t,s=ie(n,e);if(n.enteringChildren&&s){let{delayChildren:e}=s.transition||{};i.delay=nA(n.enteringChildren,t,e)}}return{animation:e,options:i}}))}if(h.size){let e={};if("boolean"!=typeof a.initial){let i=ie(t,Array.isArray(a.initial)?a.initial[0]:a.initial);i&&i.transition&&(e.transition=i.transition)}h.forEach(i=>{let n=t.getBaseTarget(i),s=t.getValue(i);s&&(s.liveStyle=!0),e[i]=n??null}),l.push({animation:e})}let m=!!l.length;return n&&(!1===a.initial||a.initial===a.animate)&&!t.manuallyAnimateOnMount&&(m=!1),n=!1,m?e(l):Promise.resolve()}return{animateChanges:r,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let s=r(e);for(let t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=nR(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();ec(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let nF=0;class nB extends nL{constructor(){super(...arguments),this.id=nF++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let nO={x:!1,y:!1};function nI(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let nU=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function nN(t){return{point:{x:t.pageX,y:t.pageY}}}let nW=t=>e=>nU(e)&&t(e,nN(e));function n$(t,e,i,n){return nI(t,e,nW(i),n)}function nz(t){return t.max-t.min}function nY(t,e,i,n=.5){t.origin=n,t.originPoint=S(e.min,e.max,t.origin),t.scale=nz(i)/nz(e),t.translate=S(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function nH(t,e,i,n){nY(t.x,e.x,i.x,n?n.originX:void 0),nY(t.y,e.y,i.y,n?n.originY:void 0)}function nX(t,e,i){t.min=i.min+e.min,t.max=t.min+nz(e)}function nK(t,e,i){t.min=e.min-i.min,t.max=t.min+nz(e)}function n_(t,e,i){nK(t.x,e.x,i.x),nK(t.y,e.y,i.y)}function nq(t){return[t("x"),t("y")]}let nG=({current:t})=>t?t.ownerDocument.defaultView:null,nZ=(t,e)=>Math.abs(t-e);class nJ{constructor(t,e,{transformPagePoint:i,contextWindow:n=window,dragSnapToOrigin:s=!1,distanceThreshold:r=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=n1(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(nZ(t.x,e.x)**2+nZ(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;let{point:n}=t,{timestamp:s}=tc;this.history.push({...n,timestamp:s});let{onStart:r,onMove:a}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=nQ(e,this.transformPagePoint),tu.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=n1("pointercancel"===t.type?this.lastMoveEventInfo:nQ(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),n&&n(t,r)},!nU(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=r,this.contextWindow=n||window;let a=nQ(nN(t),this.transformPagePoint),{point:o}=a,{timestamp:l}=tc;this.history=[{...o,timestamp:l}];let{onSessionStart:h}=e;h&&h(t,n1(a,this.history)),this.removeListeners=il(n$(this.contextWindow,"pointermove",this.handlePointerMove),n$(this.contextWindow,"pointerup",this.handlePointerUp),n$(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),td(this.updatePoint)}}function nQ(t,e){return e?{point:e(t.point)}:t}function n0(t,e){return{x:t.x-e.x,y:t.y-e.y}}function n1({point:t},e){return{point:t,delta:n0(t,n2(e)),offset:n0(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,s=n2(t);for(;i>=0&&(n=t[i],!(s.timestamp-n.timestamp>ih(.1)));)i--;if(!n)return{x:0,y:0};let r=iu(s.timestamp-n.timestamp);if(0===r)return{x:0,y:0};let a={x:(s.x-n.x)/r,y:(s.y-n.y)/r};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function n2(t){return t[t.length-1]}function n5(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function n3(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function n9(t,e,i){return{min:n4(t,e),max:n4(t,i)}}function n4(t,e){return"number"==typeof t?t:t[e]||0}let n6=new WeakMap;class n8{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=eo(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:i}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new nJ(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(nN(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:s}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(nO[t])return null;else return nO[t]=!0,()=>{nO[t]=!1};return nO.x||nO.y?null:(nO.x=nO.y=!0,()=>{nO.x=nO.y=!1})}(i),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nq(t=>{let e=this.getAxisMotionValue(t).get()||0;if(z.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=nz(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),s&&tu.postRender(()=>s(t,e)),ir(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:s,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(a),null!==this.currentDirection&&s&&s(this.currentDirection);return}this.updateAxis("x",e.point,a),this.updateAxis("y",e.point,a),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>nq(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,distanceThreshold:i,contextWindow:nG(this.visualElement)})}stop(t,e){let i=t||this.latestPointerEvent,n=e||this.latestPanInfo,s=this.isDragging;if(this.cancel(),!s||!n||!i)return;let{velocity:r}=n;this.startAnimation(r);let{onDragEnd:a}=this.getProps();a&&tu.postRender(()=>a(i,n))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!n7(t,n,this.currentDirection))return;let s=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?S(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?S(i,t,n.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),s.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&e4(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:s}){return{x:n5(t.x,i,s),y:n5(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:n9(t,"left","right"),y:n9(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&nq(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!e4(e))return!1;let n=e.current;J(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:s}=this.visualElement;if(!s||!s.layout)return!1;let r=function(t,e,i){let n=F(t,i),{scroll:s}=e;return s&&(R(n.x,s.offset.x),R(n.y,s.offset.y)),n}(n,s.root,this.visualElement.getTransformPagePoint()),a=(t=s.layout.layoutBox,{x:n3(t.x,r.x),y:n3(t.y,r.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(a));this.hasMutatedConstraints=!!t,t&&(a=P(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:s,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(nq(a=>{if(!n7(a,e,this.currentDirection))return;let l=o&&o[a]||{};r&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[a]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(a,h)})).then(a)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return ir(this.visualElement,t),i.start(nP(t,i,0,e,this.visualElement,!1))}stopAnimation(){nq(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){nq(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){nq(e=>{let{drag:i}=this.getProps();if(!n7(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,s=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:r}=n.layout.layoutBox[e];s.set(t[e]-S(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!e4(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};nq(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=nz(t),s=nz(e);return s>n?i=i5(e.min,e.max-n,t.min):n>s&&(i=i5(t.min,t.max-s,e.min)),O(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),nq(e=>{if(!n7(e,t,null))return;let i=this.getAxisMotionValue(e),{min:s,max:r}=this.constraints[e];i.set(S(s,r,n[e]))})}addListeners(){if(!this.visualElement.current)return;n6.set(this.visualElement,this);let t=n$(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();e4(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),tu.read(e);let s=nI(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(nq(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),n(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:s=!1,dragElastic:r=.35,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:s,dragElastic:r,dragMomentum:a}}}function n7(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class st extends nL{constructor(t){super(t),this.removeGroupControls=tr,this.removeListeners=tr,this.controls=new n8(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||tr}unmount(){this.removeGroupControls(),this.removeListeners()}}let se=t=>(e,i)=>{t&&tu.postRender(()=>t(e,i))};class si extends nL{constructor(){super(...arguments),this.removePointerDownListener=tr}onPointerDown(t){this.session=new nJ(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nG(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:se(t),onStart:se(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&tu.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=n$(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let sn={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ss(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let sr={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!Y.test(t))return t;else t=parseFloat(t);let i=ss(t,e.target.x),n=ss(t,e.target.y);return`${i}% ${n}%`}},sa=!1;class so extends s.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:s}=t;for(let t in sh)eC[t]=sh[t],x(t)&&(eC[t].isCSSVariable=!0);s&&(e.group&&e.group.add(s),i&&i.register&&n&&i.register(s),sa&&s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),sn.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:s}=this.props,{projection:r}=i;return r&&(r.isPresent=s,sa=!0,n||t.layoutDependency!==e||void 0===e||t.isPresent!==s?r.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?r.promote():r.relegate()||tu.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),ee.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;sa=!0,n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function sl(t){let[e,i]=function(t=!0){let e=(0,s.useContext)(e0);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:n,register:r}=e,a=(0,s.useId)();(0,s.useEffect)(()=>{if(t)return r(a)},[t]);let o=(0,s.useCallback)(()=>t&&n&&n(a),[a,n,t]);return!i&&n?[!1,o]:[!0]}(),n=(0,s.useContext)(ez);return(0,e$.jsx)(so,{...t,layoutGroup:n,switchLayoutGroup:(0,s.useContext)(e8),isPresent:e,safeToRemove:i})}let sh={borderRadius:{...sr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:sr,borderTopRightRadius:sr,borderBottomLeftRadius:sr,borderBottomRightRadius:sr,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=tW.parse(t);if(n.length>5)return t;let s=tW.createTransformer(t),r=+("number"!=typeof n[0]),a=i.x.scale*e.x,o=i.y.scale*e.y;n[0+r]/=a,n[1+r]/=o;let l=S(a,o,.5);return"number"==typeof n[2+r]&&(n[2+r]/=l),"number"==typeof n[3+r]&&(n[3+r]/=l),s(n)}}};function su(t){return"object"==typeof t&&null!==t}function sd(t){return su(t)&&"ownerSVGElement"in t}let sc=(t,e)=>t.depth-e.depth;class sp{constructor(){this.children=[],this.isDirty=!1}add(t){t2(this.children,t),this.isDirty=!0}remove(t){t5(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(sc),this.isDirty=!1,this.children.forEach(t)}}let sm=["TopLeft","TopRight","BottomLeft","BottomRight"],sf=sm.length,sg=t=>"string"==typeof t?parseFloat(t):t,sy=t=>"number"==typeof t||Y.test(t);function sv(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let sx=sT(0,.5,iZ),sw=sT(.5,.95,tr);function sT(t,e,i){return n=>n<t?0:n>e?1:i(i5(t,e,n))}function sb(t,e){t.min=e.min,t.max=e.max}function sP(t,e){sb(t.x,e.x),sb(t.y,e.y)}function sS(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function sA(t,e,i,n,s){return t-=e,t=n+1/i*(t-n),void 0!==s&&(t=n+1/s*(t-n)),t}function sM(t,e,[i,n,s],r,a){!function(t,e=0,i=1,n=.5,s,r=t,a=t){if(z.test(e)&&(e=parseFloat(e),e=S(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let o=S(r.min,r.max,n);t===r&&(o-=e),t.min=sA(t.min,e,i,o,s),t.max=sA(t.max,e,i,o,s)}(t,e[i],e[n],e[s],e.scale,r,a)}let sk=["x","scaleX","originX"],sC=["y","scaleY","originY"];function sE(t,e,i,n){sM(t.x,e,sk,i?i.x:void 0,n?n.x:void 0),sM(t.y,e,sC,i?i.y:void 0,n?n.y:void 0)}function sV(t){return 0===t.translate&&1===t.scale}function sD(t){return sV(t.x)&&sV(t.y)}function sR(t,e){return t.min===e.min&&t.max===e.max}function sL(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function sj(t,e){return sL(t.x,e.x)&&sL(t.y,e.y)}function sF(t){return nz(t.x)/nz(t.y)}function sB(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class sO{constructor(){this.members=[]}add(t){t2(this.members,t),t.scheduleRender()}remove(t){if(t5(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let sI={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},sU=["","X","Y","Z"],sN=0;function sW(t,e,i,n){let{latestValues:s}=e;s[t]&&(i[t]=s[t],e.setStaticValue(t,0),n&&(n[t]=0))}function s$({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:s}){return class{constructor(t={},i=e?.()){this.id=sN++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,tl.value&&(sI.nodes=sI.calculatedTargetDeltas=sI.calculatedProjections=0),this.nodes.forEach(sH),this.nodes.forEach(sJ),this.nodes.forEach(sQ),this.nodes.forEach(sX),tl.addProjectionMetrics&&tl.addProjectionMetrics(sI)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new sp)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new t3),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=sd(e)&&!(sd(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:n,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i,n=0,s=()=>this.root.updateBlockedByResize=!1;tu.read(()=>{n=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==n&&(n=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=t1.now(),n=({timestamp:s})=>{let r=s-i;r>=250&&(td(n),t(r-e))};return tu.setup(n,!0),()=>td(n)}(s,250),sn.hasAnimatedSinceResize&&(sn.hasAnimatedSinceResize=!1,this.nodes.forEach(sZ)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&s&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||s.getDefaultTransition()||s9,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=s.getProps(),l=!this.targetLayout||!sj(this.targetLayout,n),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...ii(r,"layout"),onPlay:a,onComplete:o};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||sZ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),td(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(s0),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[e6];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",tu,!(t||i))}let{parent:s}=e;s&&!s.hasCheckedOptimisedAppear&&t(s)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(s_);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(sq);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(sG),this.nodes.forEach(sz),this.nodes.forEach(sY)):this.nodes.forEach(sq),this.clearAllSnapshots();let t=t1.now();tc.delta=O(0,1e3/60,t-tc.timestamp),tc.timestamp=t,tc.isProcessing=!0,tp.update.process(tc),tp.preRender.process(tc),tp.render.process(tc),tc.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ee.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(sK),this.sharedNodes.forEach(s1)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,tu.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){tu.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||nz(this.snapshot.measuredBox.x)||nz(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=eo(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!sD(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,r=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||k(this.latestValues)||r)&&(s(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),s8((e=n).x),s8(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return eo();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rt))){let{scroll:t}=this.root;t&&(R(e.x,t.offset.x),R(e.y,t.offset.y))}return e}removeElementScroll(t){let e=eo();if(sP(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:s,options:r}=n;n!==this.root&&s&&r.layoutScroll&&(s.wasRoot&&sP(e,t),R(e.x,s.offset.x),R(e.y,s.offset.y))}return e}applyTransform(t,e=!1){let i=eo();sP(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&j(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),k(n.latestValues)&&j(i,n.latestValues)}return k(this.latestValues)&&j(i,this.latestValues),i}removeTransform(t){let e=eo();sP(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!k(i.latestValues))continue;M(i.latestValues)&&i.updateSnapshot();let n=eo();sP(n,i.measurePageBox()),sE(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return k(this.latestValues)&&sE(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==tc.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:s}=this.options;if(this.layout&&(n||s)){if(this.resolvedRelativeTargetAt=tc.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=eo(),this.relativeTargetOrigin=eo(),n_(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),sP(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=eo(),this.targetWithTransforms=eo()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var r,a,o;this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,o=this.relativeParent.target,nX(r.x,a.x,o.x),nX(r.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sP(this.target,this.layout.layoutBox),D(this.target,this.targetDelta)):sP(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=eo(),this.relativeTargetOrigin=eo(),n_(this.relativeTargetOrigin,this.target,t.target),sP(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}tl.value&&sI.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||M(this.parent.latestValues)||C(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===tc.timestamp&&(i=!1),i)return;let{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||s))return;sP(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,n=!1){let s,r,a=i.length;if(a){e.x=e.y=1;for(let o=0;o<a;o++){r=(s=i[o]).projectionDelta;let{visualElement:a}=s.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(n&&s.options.layoutScroll&&s.scroll&&s!==s.root&&j(t,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,D(t,r)),n&&k(s.latestValues)&&j(t,s.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=eo());let{target:o}=t;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(sS(this.prevProjectionDelta.x,this.projectionDelta.x),sS(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nH(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&sB(this.projectionDelta.x,this.prevProjectionDelta.x)&&sB(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),tl.value&&sI.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=er(),this.projectionDelta=er(),this.projectionDeltaWithTransform=er()}setAnimationOrigin(t,e=!1){let i,n=this.snapshot,s=n?n.latestValues:{},r={...this.latestValues},a=er();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let o=eo(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,d=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(s3));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(s2(a.x,t.x,n),s2(a.y,t.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,c,p,m,f,g;n_(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,g=n,s5(p.x,m.x,f.x,g),s5(p.y,m.y,f.y,g),i&&(h=this.relativeTarget,c=i,sR(h.x,c.x)&&sR(h.y,c.y))&&(this.isProjectionDirty=!1),i||(i=eo()),sP(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,n,s,r){s?(t.opacity=S(0,i.opacity??1,sx(n)),t.opacityExit=S(e.opacity??1,0,sw(n))):r&&(t.opacity=S(e.opacity??1,i.opacity??1,n));for(let s=0;s<sf;s++){let r=`border${sm[s]}Radius`,a=sv(e,r),o=sv(i,r);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||sy(a)===sy(o)?(t[r]=Math.max(S(sg(a),sg(o),n),0),(z.test(o)||z.test(a))&&(t[r]+="%")):t[r]=o)}(e.rotate||i.rotate)&&(t.rotate=S(e.rotate||0,i.rotate||0,n))}(r,s,this.latestValues,n,d,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(td(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=tu.update(()=>{sn.hasAnimatedSinceResize=!0,id.layout++,this.motionValue||(this.motionValue=t8(0)),this.currentAnimation=function(t,e,i){let n=tQ(t)?t:t8(t);return n.start(nP("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{id.layout--},onComplete:()=>{id.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:s}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&s7(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||eo();let e=nz(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=nz(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}sP(e,i),j(e,s),nH(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new sO),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&sW("z",t,n,this.animationValues);for(let e=0;e<sU.length;e++)sW(`rotate${sU[e]}`,t,n,this.animationValues),sW(`skew${sU[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=e1(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=e1(e?.pointerEvents)||""),this.hasProjected&&!k(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let s=n.animationValues||n.latestValues;this.applyTransformsToTarget();let r=function(t,e,i){let n="",s=t.x.translate/e.x,r=t.y.translate/e.y,a=i?.z||0;if((s||r||a)&&(n=`translate3d(${s}px, ${r}px, ${a}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:s,rotateY:r,skewX:a,skewY:o}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),s&&(n+=`rotateX(${s}deg) `),r&&(n+=`rotateY(${r}deg) `),a&&(n+=`skewX(${a}deg) `),o&&(n+=`skewY(${o}deg) `)}let o=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==o||1!==l)&&(n+=`scale(${o}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,s);i&&(r=i(s,r)),t.transform=r;let{x:a,y:o}=this.projectionDelta;for(let e in t.transformOrigin=`${100*a.origin}% ${100*o.origin}% 0`,n.animationValues?t.opacity=n===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:t.opacity=n===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,eC){if(void 0===s[e])continue;let{correct:i,applyTo:a,isCSSVariable:o}=eC[e],l="none"===r?s[e]:i(s[e],n);if(a){let e=a.length;for(let i=0;i<e;i++)t[a[i]]=l}else o?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=n===this?e1(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(s_),this.root.sharedNodes.clear()}}}function sz(t){t.updateLayout()}function sY(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:s}=t.options,r=e.source!==t.layout.source;"size"===s?nq(t=>{let n=r?e.measuredBox[t]:e.layoutBox[t],s=nz(n);n.min=i[t].min,n.max=n.min+s}):s7(s,e.layoutBox,i)&&nq(n=>{let s=r?e.measuredBox[n]:e.layoutBox[n],a=nz(i[n]);s.max=s.min+a,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+a)});let a=er();nH(a,i,e.layoutBox);let o=er();r?nH(o,t.applyTransform(n,!0),e.measuredBox):nH(o,i,e.layoutBox);let l=!sD(a),h=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:s,layout:r}=n;if(s&&r){let a=eo();n_(a,e.layoutBox,s.layoutBox);let o=eo();n_(o,i,r.layoutBox),sj(a,o)||(h=!0),n.options.layoutRoot&&(t.relativeTarget=o,t.relativeTargetOrigin=a,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function sH(t){tl.value&&sI.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function sX(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function sK(t){t.clearSnapshot()}function s_(t){t.clearMeasurements()}function sq(t){t.isLayoutDirty=!1}function sG(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function sZ(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function sJ(t){t.resolveTargetDelta()}function sQ(t){t.calcProjection()}function s0(t){t.resetSkewAndRotation()}function s1(t){t.removeLeadSnapshot()}function s2(t,e,i){t.translate=S(e.translate,0,i),t.scale=S(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function s5(t,e,i,n){t.min=S(e.min,i.min,n),t.max=S(e.max,i.max,n)}function s3(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let s9={duration:.45,ease:[.4,0,.1,1]},s4=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),s6=s4("applewebkit/")&&!s4("chrome/")?Math.round:tr;function s8(t){t.min=s6(t.min),t.max=s6(t.max)}function s7(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(sF(e)-sF(i)))}function rt(t){return t!==t.root&&t.scroll?.wasRoot}let re=s$({attachResizeListener:(t,e)=>nI(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ri={current:void 0},rn=s$({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ri.current){let t=new re({});t.mount(window),t.setOptions({layoutScroll:!0}),ri.current=t}return ri.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function rs(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function rr(t){return!("touch"===t.pointerType||nO.x||nO.y)}function ra(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let s=n["onHover"+i];s&&tu.postRender(()=>s(e,nN(e)))}class ro extends nL{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=rs(t,i),a=t=>{if(!rr(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let r=t=>{rr(t)&&(n(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,s)};return n.forEach(t=>{t.addEventListener("pointerenter",a,s)}),r}(t,(t,e)=>(ra(this.node,e,"Start"),t=>ra(this.node,t,"End"))))}unmount(){}}class rl extends nL{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=il(nI(this.node.current,"focus",()=>this.onFocus()),nI(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rh=(t,e)=>!!e&&(t===e||rh(t,e.parentElement)),ru=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rd=new WeakSet;function rc(t){return e=>{"Enter"===e.key&&t(e)}}function rp(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let rm=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=rc(()=>{if(rd.has(i))return;rp(i,"down");let t=rc(()=>{rp(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>rp(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function rf(t){return nU(t)&&!(nO.x||nO.y)}function rg(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let s=n["onTap"+("End"===i?"":i)];s&&tu.postRender(()=>s(e,nN(e)))}class ry extends nL{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=rs(t,i),a=t=>{let n=t.currentTarget;if(!rf(t))return;rd.add(n);let r=e(n,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),rd.has(n)&&rd.delete(n),rf(t)&&"function"==typeof r&&r(t,{success:e})},o=t=>{a(t,n===window||n===document||i.useGlobalTarget||rh(n,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,s),window.addEventListener("pointercancel",l,s)};return n.forEach(t=>{(i.useGlobalTarget?window:t).addEventListener("pointerdown",a,s),su(t)&&"offsetHeight"in t&&(t.addEventListener("focus",t=>rm(t,s)),ru.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}(t,(t,e)=>(rg(this.node,e,"Start"),(t,{success:e})=>rg(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rv=new WeakMap,rx=new WeakMap,rw=t=>{let e=rv.get(t.target);e&&e(t)},rT=t=>{t.forEach(rw)},rb={some:0,all:1};class rP extends nL{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:s}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:rb[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;rx.has(i)||rx.set(i,{});let n=rx.get(i),s=JSON.stringify(e);return n[s]||(n[s]=new IntersectionObserver(rT,{root:t,...e})),n[s]}(e);return rv.set(t,i),n.observe(t),()=>{rv.delete(t),n.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,s&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),r=e?i:n;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rS=function(t,e){if("undefined"==typeof Proxy)return it;let i=new Map,n=(i,n)=>it(i,n,t,e);return new Proxy((t,e)=>n(t,e),{get:(s,r)=>"create"===r?n:(i.has(r)||i.set(r,it(r,void 0,t,e)),i.get(r))})}({animation:{Feature:nj},exit:{Feature:nB},inView:{Feature:rP},tap:{Feature:ry},focus:{Feature:rl},hover:{Feature:ro},pan:{Feature:si},drag:{Feature:st,ProjectionNode:rn,MeasureLayout:sl},layout:{ProjectionNode:rn,MeasureLayout:sl}},(t,e)=>eW(t)?new eU(e):new eD(e,{allowProjection:t!==s.Fragment}))},4516:(t,e,i)=>{"use strict";i.d(e,{A:()=>n});let n=(0,i(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5657:(t,e,i)=>{"use strict";i.d(e,{A:()=>n});let n=(0,i(9946).A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},6325:(t,e,i)=>{"use strict";i.d(e,{A:()=>n});let n=(0,i(9946).A)("briefcase-business",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},6636:(t,e,i)=>{"use strict";i.d(e,{Hf:()=>o,QF:()=>a});var n=i(2115),s=function(t,e){return(s=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)};function r(t){var e,i,s,r,a,o,l,h,u=t.className,d=t.counterClockwise,c=t.dashRatio,p=t.pathRadius,m=t.strokeWidth,f=t.style;return(0,n.createElement)("path",{className:u,style:Object.assign({},f,(i=(e={pathRadius:p,dashRatio:c,counterClockwise:d}).counterClockwise,s=e.dashRatio,a=(1-s)*(r=2*Math.PI*e.pathRadius),{strokeDasharray:r+"px "+r+"px",strokeDashoffset:(i?-a:a)+"px"})),d:(l=(o={pathRadius:p,counterClockwise:d}).pathRadius,"\n      M 50,50\n      m 0,-"+l+"\n      a "+l+","+l+" "+(h=+!!o.counterClockwise)+" 1 1 0,"+2*l+"\n      a "+l+","+l+" "+h+" 1 1 0,-"+2*l+"\n    "),strokeWidth:m,fillOpacity:0})}var a=function(t){function e(){this.constructor=i}function i(){return null!==t&&t.apply(this,arguments)||this}return s(i,t),i.prototype=null===t?Object.create(t):(e.prototype=t.prototype,new e),i.prototype.getBackgroundPadding=function(){return this.props.background?this.props.backgroundPadding:0},i.prototype.getPathRadius=function(){return 50-this.props.strokeWidth/2-this.getBackgroundPadding()},i.prototype.getPathRatio=function(){var t=this.props,e=t.value,i=t.minValue,n=t.maxValue;return(Math.min(Math.max(e,i),n)-i)/(n-i)},i.prototype.render=function(){var t=this.props,e=t.circleRatio,i=t.className,s=t.classes,a=t.counterClockwise,o=t.styles,l=t.strokeWidth,h=t.text,u=this.getPathRadius(),d=this.getPathRatio();return(0,n.createElement)("svg",{className:s.root+" "+i,style:o.root,viewBox:"0 0 100 100","data-test-id":"CircularProgressbar"},this.props.background?(0,n.createElement)("circle",{className:s.background,style:o.background,cx:50,cy:50,r:50}):null,(0,n.createElement)(r,{className:s.trail,counterClockwise:a,dashRatio:e,pathRadius:u,strokeWidth:l,style:o.trail}),(0,n.createElement)(r,{className:s.path,counterClockwise:a,dashRatio:d*e,pathRadius:u,strokeWidth:l,style:o.path}),h?(0,n.createElement)("text",{className:s.text,style:o.text,x:50,y:50},h):null)},i.defaultProps={background:!1,backgroundPadding:0,circleRatio:1,classes:{root:"CircularProgressbar",trail:"CircularProgressbar-trail",path:"CircularProgressbar-path",text:"CircularProgressbar-text",background:"CircularProgressbar-background"},counterClockwise:!1,className:"",maxValue:100,minValue:0,strokeWidth:8,styles:{root:{},trail:{},path:{},text:{},background:{}},text:""},i}(n.Component);function o(t){var e=t.rotation,i=t.strokeLinecap,n=t.textColor,s=t.textSize,r=t.pathColor,a=t.pathTransition,o=t.pathTransitionDuration,h=t.trailColor,u=t.backgroundColor,d=null==e?void 0:"rotate("+e+"turn)",c=null==e?void 0:"center center";return{root:{},path:l({stroke:r,strokeLinecap:i,transform:d,transformOrigin:c,transition:a,transitionDuration:null==o?void 0:o+"s"}),trail:l({stroke:h,strokeLinecap:i,transform:d,transformOrigin:c}),text:l({fill:n,fontSize:s}),background:l({fill:u})}}function l(t){return Object.keys(t).forEach(function(e){null==t[e]&&delete t[e]}),t}},9946:(t,e,i)=>{"use strict";i.d(e,{A:()=>d});var n=i(2115);let s=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,i)=>i?i.toUpperCase():e.toLowerCase()),a=t=>{let e=r(t);return e.charAt(0).toUpperCase()+e.slice(1)},o=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim()},l=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};var h={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)((t,e)=>{let{color:i="currentColor",size:s=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:u="",children:d,iconNode:c,...p}=t;return(0,n.createElement)("svg",{ref:e,...h,width:s,height:s,stroke:i,strokeWidth:a?24*Number(r)/Number(s):r,className:o("lucide",u),...!d&&!l(p)&&{"aria-hidden":"true"},...p},[...c.map(t=>{let[e,i]=t;return(0,n.createElement)(e,i)}),...Array.isArray(d)?d:[d]])}),d=(t,e)=>{let i=(0,n.forwardRef)((i,r)=>{let{className:l,...h}=i;return(0,n.createElement)(u,{ref:r,iconNode:e,className:o("lucide-".concat(s(a(t))),"lucide-".concat(t),l),...h})});return i.displayName=a(t),i}}}]);